#!/usr/bin/env python3
"""
Update database schema to add capital_price column and fix data
"""

from app import app, db, Product
import os

def update_schema():
    """Update the database schema and add capital prices"""
    with app.app_context():
        print("🔧 Updating database schema...")
        
        # Check if capital_price column exists
        try:
            # Try to access capital_price - if it fails, we need to add it
            test_product = Product.query.first()
            if test_product:
                _ = test_product.capital_price
                print("✅ capital_price column already exists")
        except Exception:
            print("📝 Adding capital_price column...")
            
            # Add the column using raw SQL
            db.engine.execute('ALTER TABLE product ADD COLUMN capital_price FLOAT DEFAULT 0.0')
            db.session.commit()
            print("✅ capital_price column added")
        
        # Update capital prices (assuming 70% of selling price as capital)
        print("💰 Setting capital prices...")
        products = Product.query.all()
        
        for product in products:
            if product.capital_price == 0.0:  # Only update if not set
                # Set capital price to 70% of selling price
                product.capital_price = round(product.price * 0.7, 2)
        
        db.session.commit()
        print("✅ Capital prices updated")
        
        # Verify the data
        print("\n📊 Product Data Verification:")
        print(f"{'Product':<20} {'Selling':<8} {'Capital':<8} {'Profit':<8} {'Received':<8} {'Sold':<6} {'Stock':<6}")
        print("-" * 70)
        
        for product in Product.query.all():
            profit = product.price - product.capital_price
            print(f"{product.name:<20} ₱{product.price:<7.2f} ₱{product.capital_price:<7.2f} ₱{profit:<7.2f} {product.received:<8} {product.sold:<6} {product.stock:<6}")

if __name__ == "__main__":
    update_schema()
