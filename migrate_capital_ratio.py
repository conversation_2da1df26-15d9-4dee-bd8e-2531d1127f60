#!/usr/bin/env python3
"""
Add capital_ratio column to existing database
"""

from app import app, db, Product
import sqlite3
import os

def migrate_database():
    """Add capital_ratio column to existing products"""
    with app.app_context():
        print("🔧 Migrating database to add capital_ratio column...")
        
        # Check if column already exists
        try:
            # Try to access capital_ratio - if it works, column exists
            test_product = Product.query.first()
            if test_product and hasattr(test_product, 'capital_ratio'):
                print("✅ capital_ratio column already exists")
                return
        except:
            pass
        
        # Get database path
        db_path = 'instance/frozen_foods_pos.db'
        if not os.path.exists(db_path):
            print("❌ Database not found. Please run the app first to create it.")
            return
        
        # Add the column using raw SQL
        try:
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # Add capital_ratio column with default value 0.7 (70%)
                cursor.execute('ALTER TABLE product ADD COLUMN capital_ratio FLOAT DEFAULT 0.7')
                conn.commit()
                print("✅ Added capital_ratio column successfully")
                
                # Update all existing products to have 0.7 ratio
                cursor.execute('UPDATE product SET capital_ratio = 0.7 WHERE capital_ratio IS NULL')
                conn.commit()
                print("✅ Set default capital ratio for existing products")
                
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e):
                print("✅ capital_ratio column already exists")
            else:
                print(f"❌ Error adding column: {e}")
                return
        
        print("🎉 Database migration completed successfully!")
        
        # Verify the migration
        print("\n📊 Verifying migration...")
        products = Product.query.all()
        for product in products[:3]:  # Show first 3 products
            print(f"  {product.name}: Selling=₱{product.price:.2f}, Capital=₱{product.capital_price:.2f}, Profit=₱{product.profit_per_unit:.2f}")

if __name__ == "__main__":
    migrate_database()
