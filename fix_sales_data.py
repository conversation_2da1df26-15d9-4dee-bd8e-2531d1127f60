#!/usr/bin/env python3
"""
Fix sales data to match actual stock levels
"""

from app import app, db, Product, Sale, Transaction, TransactionItem, Customer, CreditTransaction
from datetime import datetime, timedelta
import random

def fix_sales_data():
    """Fix sales data to match the actual stock levels"""
    with app.app_context():
        print("🔧 Fixing sales data to match stock levels...")
        
        # Clear existing sales data
        print("📝 Clearing existing sales data...")
        Sale.query.delete()
        TransactionItem.query.delete()
        Transaction.query.delete()
        CreditTransaction.query.delete()
        
        # Get all products
        products = Product.query.all()
        customers = Customer.query.all()
        
        print(f"📦 Found {len(products)} products")
        
        # Generate sales based on actual sold quantities
        transaction_number = 1
        
        for product in products:
            if product.sold > 0:
                print(f"📊 Creating {product.sold} sales for {product.name}")
                
                # Create multiple transactions for this product
                remaining_sold = product.sold
                
                while remaining_sold > 0:
                    # Random transaction date in the last 30 days
                    days_ago = random.randint(0, 29)
                    transaction_date = datetime.now() - timedelta(days=days_ago)
                    
                    # Random quantity for this transaction (1-3 items, but not more than remaining)
                    quantity = min(random.randint(1, 3), remaining_sold)
                    remaining_sold -= quantity
                    
                    # Calculate totals
                    unit_price = product.price
                    total_price = quantity * unit_price
                    
                    # Create transaction
                    transaction = Transaction(
                        transaction_number=f"TXN{transaction_number:06d}",
                        total_amount=total_price,
                        transaction_date=transaction_date
                    )
                    db.session.add(transaction)
                    db.session.flush()
                    
                    # Create transaction item
                    transaction_item = TransactionItem(
                        transaction_id=transaction.id,
                        product_id=product.id,
                        quantity=quantity,
                        unit_price=unit_price,
                        total_price=total_price
                    )
                    db.session.add(transaction_item)
                    
                    # Create sale record
                    sale = Sale(
                        product_id=product.id,
                        quantity=quantity,
                        unit_price=unit_price,
                        total_price=total_price,
                        sale_date=transaction_date
                    )
                    db.session.add(sale)
                    
                    # 30% chance of credit transaction
                    if customers and random.random() < 0.3:
                        customer = random.choice(customers)
                        credit_transaction = CreditTransaction(
                            customer_id=customer.id,
                            transaction_id=transaction.id,
                            amount=total_price,
                            transaction_type='credit',
                            is_paid=random.choice([True, False])
                        )
                        db.session.add(credit_transaction)
                    
                    transaction_number += 1
        
        # Commit all changes
        db.session.commit()
        print("✅ Sales data fixed successfully!")
        
        # Verify the data
        print("\n📊 Verification:")
        for product in Product.query.all():
            total_sales = db.session.query(db.func.sum(Sale.total_price)).filter_by(product_id=product.id).scalar() or 0
            total_quantity = db.session.query(db.func.sum(Sale.quantity)).filter_by(product_id=product.id).scalar() or 0
            print(f"  {product.name}: {total_quantity} units sold, ₱{total_sales:.2f} revenue")

if __name__ == "__main__":
    fix_sales_data()
