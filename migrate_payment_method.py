#!/usr/bin/env python3
"""
Migration script to add payment_method column to Transaction table
and set default values based on existing credit transactions.
"""

from app import app, db, Transaction, CreditTransaction

def migrate_payment_method():
    """Add payment_method column and set values based on existing data"""
    
    with app.app_context():
        print("🔄 Starting payment method migration...")
        print("=" * 50)
        
        try:
            # Check if column already exists
            inspector = db.inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('transaction')]
            
            if 'payment_method' in columns:
                print("✅ payment_method column already exists")
            else:
                print("❌ payment_method column not found - please restart the app to create it")
                return
            
            # Get all transactions
            transactions = Transaction.query.all()
            print(f"📊 Found {len(transactions)} transactions to update")
            
            # Get all credit transaction IDs
            credit_transaction_ids = set()
            credit_transactions = CreditTransaction.query.all()
            for ct in credit_transactions:
                if ct.transaction_id:
                    credit_transaction_ids.add(ct.transaction_id)
            
            print(f"💳 Found {len(credit_transaction_ids)} credit transactions")
            
            # Update payment methods
            updated_count = 0
            for transaction in transactions:
                if transaction.payment_method is None:  # Only update if not set
                    if transaction.id in credit_transaction_ids:
                        transaction.payment_method = 'credit'
                    else:
                        transaction.payment_method = 'cash'
                    updated_count += 1
            
            # Commit changes
            db.session.commit()
            
            print(f"✅ Updated {updated_count} transactions")
            print("\n📊 FINAL SUMMARY:")
            
            # Show final counts
            cash_count = Transaction.query.filter_by(payment_method='cash').count()
            credit_count = Transaction.query.filter_by(payment_method='credit').count()
            
            print(f"   💰 Cash transactions: {cash_count}")
            print(f"   💳 Credit transactions: {credit_count}")
            print(f"   📋 Total transactions: {cash_count + credit_count}")
            
            print("\n🎉 Migration completed successfully!")
            
        except Exception as e:
            print(f"❌ Migration failed: {str(e)}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    migrate_payment_method()
