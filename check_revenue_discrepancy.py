#!/usr/bin/env python3
"""
Check revenue discrepancy between Sale and Transaction tables
"""

from app import app, db, Sale, Transaction, TransactionItem

def check_revenue_discrepancy():
    with app.app_context():
        print("🔍 Checking Revenue Discrepancy...")
        print("=" * 50)
        
        # Check Sale table total
        sale_total = db.session.query(db.func.sum(Sale.total_price)).scalar() or 0
        print(f'📊 Sale table total: ₱{sale_total:.2f}')
        
        # Check Transaction table total  
        transaction_total = db.session.query(db.func.sum(Transaction.total_amount)).scalar() or 0
        print(f'💰 Transaction table total: ₱{transaction_total:.2f}')
        
        # Check TransactionItem table total
        transaction_item_total = db.session.query(db.func.sum(TransactionItem.total_price)).scalar() or 0
        print(f'🧾 TransactionItem table total: ₱{transaction_item_total:.2f}')
        
        print(f'\n🔍 Discrepancy: ₱{abs(sale_total - transaction_total):.2f}')
        
        # Count records
        sale_count = Sale.query.count()
        transaction_count = Transaction.query.count()
        transaction_item_count = TransactionItem.query.count()
        
        print(f'\n📈 Record Counts:')
        print(f'   Sale records: {sale_count}')
        print(f'   Transaction records: {transaction_count}')
        print(f'   TransactionItem records: {transaction_item_count}')
        
        # Check for recent records
        print('\n📋 Recent Sales (last 5):')
        for sale in Sale.query.order_by(Sale.sale_date.desc()).limit(5):
            print(f'   Sale: ₱{sale.total_price:.2f} - {sale.sale_date.strftime("%Y-%m-%d %H:%M")}')
        
        print('\n📋 Recent Transactions (last 5):')
        for trans in Transaction.query.order_by(Transaction.transaction_date.desc()).limit(5):
            print(f'   Transaction {trans.transaction_number}: ₱{trans.total_amount:.2f} - {trans.transaction_date.strftime("%Y-%m-%d %H:%M")}')
        
        # Check if there are sales without transactions or vice versa
        print('\n🔍 Data Integrity Check:')
        
        # Get all sale dates and transaction dates
        sale_dates = [s.sale_date.date() for s in Sale.query.all()]
        transaction_dates = [t.transaction_date.date() for t in Transaction.query.all()]
        
        # Group by date and sum
        from collections import defaultdict
        sale_by_date = defaultdict(float)
        transaction_by_date = defaultdict(float)
        
        for sale in Sale.query.all():
            sale_by_date[sale.sale_date.date()] += sale.total_price
            
        for trans in Transaction.query.all():
            transaction_by_date[trans.transaction_date.date()] += trans.total_amount
        
        print('\n📅 Daily Comparison:')
        all_dates = set(sale_by_date.keys()) | set(transaction_by_date.keys())
        for date in sorted(all_dates, reverse=True):
            sale_amount = sale_by_date.get(date, 0)
            trans_amount = transaction_by_date.get(date, 0)
            diff = abs(sale_amount - trans_amount)
            status = "✅" if diff < 0.01 else "❌"
            print(f'   {date}: Sales ₱{sale_amount:.2f} | Transactions ₱{trans_amount:.2f} | Diff ₱{diff:.2f} {status}')

if __name__ == "__main__":
    check_revenue_discrepancy()
