{% extends "base.html" %} {% block title %}Inventory Management - ATE MEG's
FROZEN FOODS{% endblock %} {% block content %}
<!-- Modern Inventory Header -->
<div class="row mb-4">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <h1 class="display-6 fw-bold text-primary mb-2">
          <i class="fas fa-warehouse me-3"></i>Inventory Management
        </h1>
        <p class="text-muted fs-5">
          Stock levels, profit analysis, and inventory insights
        </p>
      </div>
      <div>
        <a href="{{ url_for('reports') }}" class="btn btn-outline-primary me-2">
          <i class="fas fa-arrow-left me-2"></i>Back to Reports
        </a>
        <a href="{{ url_for('inventory_movements') }}" class="btn btn-primary">
          <i class="fas fa-exchange-alt me-2"></i>View Movements
        </a>
      </div>
    </div>
  </div>
</div>

<!-- Inventory Overview Metrics -->
<div class="row mb-5">
  <div class="col-md-3 mb-3">
    <div class="card card-gradient-primary text-white h-100">
      <div class="card-body text-center">
        <div class="mb-3">
          <i class="fas fa-boxes fa-2x opacity-75"></i>
        </div>
        <h6 class="text-uppercase fw-bold opacity-75 mb-2">Total Products</h6>
        <h3 class="display-6 fw-bold mb-0">{{ total_products }}</h3>
      </div>
    </div>
  </div>
  <div class="col-md-3 mb-3">
    <div class="card card-gradient-success text-white h-100">
      <div class="card-body text-center">
        <div class="mb-3">
          <i class="fas fa-cubes fa-2x opacity-75"></i>
        </div>
        <h6 class="text-uppercase fw-bold opacity-75 mb-2">
          Total Stock Units
        </h6>
        <h3 class="display-6 fw-bold mb-0">{{ total_stock_units }}</h3>
      </div>
    </div>
  </div>
  <div class="col-md-3 mb-3">
    <div class="card card-gradient-warning text-white h-100">
      <div class="card-body text-center">
        <div class="mb-3">
          <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
        </div>
        <h6 class="text-uppercase fw-bold opacity-75 mb-2">Low Stock Alert</h6>
        <h3 class="display-6 fw-bold mb-0">{{ low_stock_products|length }}</h3>
      </div>
    </div>
  </div>
  <div class="col-md-3 mb-3">
    <div class="card card-gradient-danger text-white h-100">
      <div class="card-body text-center">
        <div class="mb-3">
          <i class="fas fa-times-circle fa-2x opacity-75"></i>
        </div>
        <h6 class="text-uppercase fw-bold opacity-75 mb-2">Out of Stock</h6>
        <h3 class="display-6 fw-bold mb-0">
          {{ out_of_stock_products|length }}
        </h3>
      </div>
    </div>
  </div>
</div>

<!-- Profit Summary -->
<div class="row mb-4">
  <div class="col-md-4">
    <div class="card text-white bg-info">
      <div class="card-body text-center">
        <h5>Total Revenue</h5>
        <h3>{{ summary.total_revenue|currency }}</h3>
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="card text-white bg-success">
      <div class="card-body text-center">
        <h5>Total Profit</h5>
        <h3>{{ summary.total_profit|currency }}</h3>
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="card text-white bg-secondary">
      <div class="card-body text-center">
        <h5>Stock Value</h5>
        <h3>{{ summary.total_stock_value|currency }}</h3>
      </div>
    </div>
  </div>
</div>

<!-- Low Stock Alert -->
{% if summary.low_stock_count > 0 or summary.out_of_stock_count > 0 %}
<div class="row mb-4">
  <div class="col-12">
    <div class="alert alert-warning">
      <h5 class="alert-heading">
        <i class="fas fa-exclamation-triangle"></i> Stock Alert
      </h5>
      <p class="mb-0">
        {% if summary.out_of_stock_count > 0 %} {{ summary.out_of_stock_count }}
        product(s) are out of stock. {% endif %} {% if summary.low_stock_count >
        0 %} {{ summary.low_stock_count }} product(s) have low stock levels. {%
        endif %} Consider restocking these items soon.
      </p>
    </div>
  </div>
</div>
{% endif %}

<!-- Inventory Table -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-table"></i> Detailed Inventory Report
        </h5>
      </div>
      <div class="card-body">
        {% if products %}
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead class="table-dark">
              <tr>
                <th>Product</th>
                <th>Selling Price</th>
                <th>Capital Price</th>
                <th>Profit/Unit</th>
                <th>Received</th>
                <th>Sold</th>
                <th>Current Stock</th>
                <th>Stock Value</th>
                <th>Total Profit</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              {% for product in products %}
              <tr
                class="{% if product.stock == 0 %}table-danger{% elif product.stock <= 5 %}table-warning{% endif %}"
              >
                <td>
                  <strong>{{ product.name }}</strong>
                </td>
                <td>{{ product.price|currency }}</td>
                <td>{{ product.capital_price|currency }}</td>
                <td class="text-success">
                  {{ product.profit_per_unit|currency }}
                </td>
                <td>{{ product.received }}</td>
                <td>{{ product.sold }}</td>
                <td>
                  <span
                    class="badge {% if product.stock == 0 %}bg-danger{% elif product.stock <= 5 %}bg-warning{% else %}bg-success{% endif %}"
                  >
                    {{ product.stock }}
                  </span>
                </td>
                <td class="text-info">{{ product.stock_value|currency }}</td>
                <td class="text-success">
                  <strong>{{ product.total_profit|currency }}</strong>
                </td>
                <td>
                  {% if product.stock == 0 %}
                  <span class="badge bg-danger">Out of Stock</span>
                  {% elif product.stock <= 5 %}
                  <span class="badge bg-warning">Low Stock</span>
                  {% elif product.stock <= 10 %}
                  <span class="badge bg-info">Medium Stock</span>
                  {% else %}
                  <span class="badge bg-success">Well Stocked</span>
                  {% endif %}
                </td>
              </tr>
              {% endfor %}
            </tbody>
            <tfoot class="table-secondary">
              <tr>
                <th colspan="4">TOTALS</th>
                <th>{{ products|sum(attribute='received') }}</th>
                <th>{{ products|sum(attribute='sold') }}</th>
                <th>{{ summary.total_stock_units }}</th>
                <th>{{ summary.total_stock_value|currency }}</th>
                <th>{{ summary.total_profit|currency }}</th>
                <th>{{ summary.total_products }} Items</th>
              </tr>
            </tfoot>
          </table>
        </div>
        {% else %}
        <div class="text-center py-4">
          <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
          <h5 class="text-muted">No products found</h5>
          <p class="text-muted">
            Add some products to see the inventory report.
          </p>
          <a href="{{ url_for('add_product') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Product
          </a>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- Profit Analysis -->
{% if products %}
<div class="row mt-4">
  <div class="col-md-6">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-chart-pie"></i> Top Profitable Products
        </h5>
      </div>
      <div class="card-body">
        {% for product in products|sort(attribute='total_profit', reverse=true)
        %} {% if loop.index <= 5 %}
        <div class="d-flex justify-content-between align-items-center mb-2">
          <div>
            <strong>{{ product.name }}</strong><br />
            <small class="text-muted">{{ product.sold }} units sold</small>
          </div>
          <div class="text-end">
            <span class="badge bg-success"
              >{{ product.total_profit|currency }}</span
            >
          </div>
        </div>
        {% if not loop.last and loop.index < 5 %}
        <hr class="my-2" />
        {% endif %} {% endif %} {% endfor %}
      </div>
    </div>
  </div>
  <div class="col-md-6">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-exclamation-triangle"></i> Stock Alerts
        </h5>
      </div>
      <div class="card-body">
        {% for product in products if product.stock <= 5 %}
        <div class="d-flex justify-content-between align-items-center mb-2">
          <div>
            <strong>{{ product.name }}</strong><br />
            <small class="text-muted">{{ product.status }}</small>
          </div>
          <div class="text-end">
            <span
              class="badge {% if product.stock == 0 %}bg-danger{% else %}bg-warning{% endif %}"
            >
              {{ product.stock }} left
            </span>
          </div>
        </div>
        {% if not loop.last %}
        <hr class="my-2" />
        {% endif %} {% else %}
        <p class="text-success mb-0">
          <i class="fas fa-check-circle"></i> All products are well stocked!
        </p>
        {% endfor %}
      </div>
    </div>
  </div>
</div>
{% endif %} {% endblock %}
