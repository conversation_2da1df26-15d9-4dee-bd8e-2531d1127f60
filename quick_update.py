#!/usr/bin/env python3
"""
Quick update script for individual product changes
"""

from app import app, db, Product

def update_single_product(name, price=None, add_stock=None):
    """Update a single product's price or add stock"""
    with app.app_context():
        product = Product.query.filter_by(name=name).first()
        if not product:
            print(f"❌ Product '{name}' not found")
            return
        
        if price is not None:
            old_price = product.price
            product.price = price
            print(f"✅ {name}: Price updated ₱{old_price:.2f} → ₱{price:.2f}")
        
        if add_stock is not None:
            product.received += add_stock
            product.stock += add_stock
            print(f"✅ {name}: Added {add_stock} units. New stock: {product.stock}")
        
        db.session.commit()

# Example usage:
if __name__ == "__main__":
    # Update individual products as needed
    # update_single_product("Beef Burger Patty", price=85)  # Change price to ₱85
    # update_single_product("Chicken Nuggets", add_stock=10)  # Add 10 units to stock
    
    print("🏪 Quick Update Tool Ready!")
    print("Uncomment the lines above to make updates")
