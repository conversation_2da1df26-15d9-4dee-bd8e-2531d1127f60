#!/usr/bin/env python3
"""
Comprehensive CSV backup system for Frozen Foods POS
Exports all database tables to CSV files with timestamps
"""

import os
import csv
import pandas as pd
from datetime import datetime
from app import app, db, Product, Sale, Transaction, TransactionItem, Customer, CreditTransaction, Cashier

def create_backup_directory():
    """Create backup directory with timestamp"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backups/backup_{timestamp}"
    os.makedirs(backup_dir, exist_ok=True)
    return backup_dir

def export_to_csv():
    """Export all database tables to CSV files"""
    with app.app_context():
        print("📁 Creating CSV backup of entire database...")
        
        # Create backup directory
        backup_dir = create_backup_directory()
        print(f"📂 Backup directory: {backup_dir}")
        
        # 1. Export Cashiers
        print("👥 Exporting cashiers...")
        cashiers = Cashier.query.all()
        cashier_data = []
        for cashier in cashiers:
            cashier_data.append({
                'id': cashier.id,
                'name': cashier.name,
                'is_active': cashier.is_active,
                'created_at': cashier.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': cashier.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        
        df_cashiers = pd.DataFrame(cashier_data)
        cashier_file = os.path.join(backup_dir, 'cashiers.csv')
        df_cashiers.to_csv(cashier_file, index=False)
        print(f"   ✅ {len(cashier_data)} cashiers exported to {cashier_file}")
        
        # 2. Export Products
        print("📦 Exporting products...")
        products = Product.query.all()
        product_data = []
        for product in products:
            product_data.append({
                'id': product.id,
                'name': product.name,
                'price': product.price,
                'capital_ratio': product.capital_ratio,
                'capital_price': product.capital_price,
                'profit_per_unit': product.profit_per_unit,
                'stock': product.stock,
                'received': product.received,
                'sold': product.sold,
                'total_revenue': product.total_revenue,
                'total_profit': product.total_profit,
                'stock_value': product.stock * product.price,
                'created_at': product.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': product.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        
        df_products = pd.DataFrame(product_data)
        product_file = os.path.join(backup_dir, 'products.csv')
        df_products.to_csv(product_file, index=False)
        print(f"   ✅ {len(product_data)} products exported to {product_file}")
        
        # 3. Export Customers
        print("👤 Exporting customers...")
        customers = Customer.query.all()
        customer_data = []
        for customer in customers:
            customer_data.append({
                'id': customer.id,
                'name': customer.name,
                'phone': customer.phone,
                'address': customer.address,
                'total_credit': customer.total_credit,
                'total_paid': customer.total_paid,
                'balance': customer.balance,
                'created_at': customer.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': customer.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        
        df_customers = pd.DataFrame(customer_data)
        customer_file = os.path.join(backup_dir, 'customers.csv')
        df_customers.to_csv(customer_file, index=False)
        print(f"   ✅ {len(customer_data)} customers exported to {customer_file}")
        
        # 4. Export Transactions
        print("💰 Exporting transactions...")
        transactions = Transaction.query.all()
        transaction_data = []
        for transaction in transactions:
            cashier_name = transaction.cashier.name if transaction.cashier else "No Cashier"
            transaction_data.append({
                'id': transaction.id,
                'transaction_number': transaction.transaction_number,
                'total_amount': transaction.total_amount,
                'cashier_id': transaction.cashier_id,
                'cashier_name': cashier_name,
                'transaction_date': transaction.transaction_date.strftime('%Y-%m-%d %H:%M:%S'),
                'date_only': transaction.transaction_date.strftime('%Y-%m-%d'),
                'time_only': transaction.transaction_date.strftime('%H:%M:%S')
            })
        
        df_transactions = pd.DataFrame(transaction_data)
        transaction_file = os.path.join(backup_dir, 'transactions.csv')
        df_transactions.to_csv(transaction_file, index=False)
        print(f"   ✅ {len(transaction_data)} transactions exported to {transaction_file}")
        
        # 5. Export Transaction Items (Detailed breakdown)
        print("🧾 Exporting transaction items...")
        transaction_items = TransactionItem.query.all()
        transaction_item_data = []
        for item in transaction_items:
            transaction = item.transaction
            product = item.product
            cashier_name = transaction.cashier.name if transaction.cashier else "No Cashier"
            
            transaction_item_data.append({
                'id': item.id,
                'transaction_id': item.transaction_id,
                'transaction_number': transaction.transaction_number if transaction else "Unknown",
                'product_id': item.product_id,
                'product_name': product.name if product else "Unknown Product",
                'quantity': item.quantity,
                'unit_price': item.unit_price,
                'total_price': item.total_price,
                'cashier_name': cashier_name,
                'transaction_date': transaction.transaction_date.strftime('%Y-%m-%d %H:%M:%S') if transaction else "Unknown"
            })
        
        df_transaction_items = pd.DataFrame(transaction_item_data)
        transaction_item_file = os.path.join(backup_dir, 'transaction_items.csv')
        df_transaction_items.to_csv(transaction_item_file, index=False)
        print(f"   ✅ {len(transaction_item_data)} transaction items exported to {transaction_item_file}")
        
        # 6. Export Sales
        print("📈 Exporting sales...")
        sales = Sale.query.all()
        sale_data = []
        for sale in sales:
            product = sale.product
            transaction = sale.transaction
            cashier_name = transaction.cashier.name if transaction and transaction.cashier else "No Cashier"
            
            sale_data.append({
                'id': sale.id,
                'product_id': sale.product_id,
                'product_name': product.name if product else "Unknown Product",
                'transaction_id': sale.transaction_id,
                'transaction_number': transaction.transaction_number if transaction else "Unknown",
                'quantity': sale.quantity,
                'unit_price': sale.unit_price,
                'total_price': sale.total_price,
                'cashier_name': cashier_name,
                'sale_date': sale.sale_date.strftime('%Y-%m-%d %H:%M:%S'),
                'date_only': sale.sale_date.strftime('%Y-%m-%d'),
                'time_only': sale.sale_date.strftime('%H:%M:%S')
            })
        
        df_sales = pd.DataFrame(sale_data)
        sale_file = os.path.join(backup_dir, 'sales.csv')
        df_sales.to_csv(sale_file, index=False)
        print(f"   ✅ {len(sale_data)} sales exported to {sale_file}")
        
        # 7. Export Credit Transactions
        print("💳 Exporting credit transactions...")
        credit_transactions = CreditTransaction.query.all()
        credit_data = []
        for credit in credit_transactions:
            customer = credit.customer
            transaction = credit.transaction
            
            credit_data.append({
                'id': credit.id,
                'customer_id': credit.customer_id,
                'customer_name': customer.name if customer else "Unknown Customer",
                'transaction_id': credit.transaction_id,
                'transaction_number': transaction.transaction_number if transaction else "No Transaction",
                'amount': credit.amount,
                'transaction_type': credit.transaction_type,
                'description': credit.description,
                'is_paid': credit.is_paid,
                'created_at': credit.created_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        
        df_credits = pd.DataFrame(credit_data)
        credit_file = os.path.join(backup_dir, 'credit_transactions.csv')
        df_credits.to_csv(credit_file, index=False)
        print(f"   ✅ {len(credit_data)} credit transactions exported to {credit_file}")
        
        # 8. Create Summary Report
        print("📊 Creating summary report...")
        summary_data = {
            'backup_timestamp': [datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
            'total_cashiers': [len(cashier_data)],
            'total_products': [len(product_data)],
            'total_customers': [len(customer_data)],
            'total_transactions': [len(transaction_data)],
            'total_transaction_items': [len(transaction_item_data)],
            'total_sales': [len(sale_data)],
            'total_credit_transactions': [len(credit_data)],
            'total_revenue': [sum(t['total_amount'] for t in transaction_data)],
            'total_stock_value': [sum(p['stock_value'] for p in product_data)],
            'outstanding_credit': [sum(c['balance'] for c in customer_data)]
        }
        
        df_summary = pd.DataFrame(summary_data)
        summary_file = os.path.join(backup_dir, 'backup_summary.csv')
        df_summary.to_csv(summary_file, index=False)
        print(f"   ✅ Summary report exported to {summary_file}")
        
        # Create a master backup log
        log_file = "backups/backup_log.csv"
        log_entry = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'backup_directory': backup_dir,
            'total_records': (len(cashier_data) + len(product_data) + len(customer_data) + 
                            len(transaction_data) + len(sale_data) + len(credit_data)),
            'total_revenue': sum(t['total_amount'] for t in transaction_data),
            'status': 'SUCCESS'
        }
        
        # Append to log file
        file_exists = os.path.isfile(log_file)
        with open(log_file, 'a', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=log_entry.keys())
            if not file_exists:
                writer.writeheader()
            writer.writerow(log_entry)
        
        print(f"\n🎉 Backup completed successfully!")
        print(f"📂 Backup location: {backup_dir}")
        print(f"📋 Files created:")
        print(f"   - cashiers.csv ({len(cashier_data)} records)")
        print(f"   - products.csv ({len(product_data)} records)")
        print(f"   - customers.csv ({len(customer_data)} records)")
        print(f"   - transactions.csv ({len(transaction_data)} records)")
        print(f"   - transaction_items.csv ({len(transaction_item_data)} records)")
        print(f"   - sales.csv ({len(sale_data)} records)")
        print(f"   - credit_transactions.csv ({len(credit_data)} records)")
        print(f"   - backup_summary.csv")
        print(f"📝 Backup logged to: {log_file}")
        
        return backup_dir

if __name__ == "__main__":
    export_to_csv()
