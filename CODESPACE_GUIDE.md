# 🚀 GitHub Codespace Quick Start Guide

## ⚡ Instant Setup (Automatic)

When you open this repository in GitHub Codespaces, everything is set up automatically:

1. **Python 3.10** environment is created
2. **Dependencies** are installed automatically
3. **Database** is initialized with sample data
4. **Port 5000** is forwarded and opens in browser

## 🎯 Manual Start (If Needed)

If the app doesn't start automatically, run:

```bash
python start.py
```

This will:
- ✅ Check database status
- ✅ Install any missing dependencies
- ✅ Initialize database if needed
- ✅ Start the POS system
- ✅ Open in browser automatically

## 🏪 Using the POS System

### **Main Features Available:**
- **POS Interface** - Process sales with cash change calculation
- **Inventory Management** - Track stock levels and movements
- **Sales Reports** - Advanced filtering and analytics
- **Customer Management** - Credit tracking (pautang system)

### **Quick Navigation:**
- **Home**: http://localhost:5000
- **POS System**: http://localhost:5000/pos
- **Reports**: http://localhost:5000/reports
- **Products**: http://localhost:5000/products
- **Customers**: http://localhost:5000/customers

## 📱 Testing the System

### **Sample Data Included:**
- **18 Frozen Food Products** (₱60-₱110)
- **5 Sample Customers** for credit testing
- **Sample Sales Transactions** for reports
- **Complete inventory** with stock levels

### **Try These Features:**
1. **Process a Cash Sale**:
   - Go to POS → Add products → Enter cash amount → Process sale

2. **Process a Credit Sale**:
   - Go to POS → Add products → Select Credit → Choose customer → Process

3. **View Reports**:
   - Go to Reports → Try different filters and date ranges

4. **Manage Inventory**:
   - Go to Products → Add/Edit products and stock levels

## 🔧 Troubleshooting

### **If the app won't start:**
```bash
# Check if dependencies are installed
pip install -r requirements.txt

# Initialize database
python init_data.py

# Start the app
python app.py
```

### **If port 5000 is busy:**
```bash
# Kill any process using port 5000
pkill -f "python.*app.py"

# Start again
python start.py
```

### **If database issues:**
```bash
# Remove and recreate database
rm pos_system.db
python init_data.py
python create_sample_sales.py
```

## 🎉 Ready to Use!

Your ATE MEG's FROZEN FOODS POS System is now running and ready for business operations!

**Access the system**: Click the "Open in Browser" notification or go to http://localhost:5000

**Need help?** Check the main README.md for detailed documentation.
