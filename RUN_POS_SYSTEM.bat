@echo off
title ATE MEG's FROZEN FOODS POS System
color 0A

echo.
echo  ████████████████████████████████████████████████████████
echo  █                                                      █
echo  █        🏪 ATE MEG's FROZEN FOODS POS SYSTEM          █
echo  █                                                      █
echo  █              Universal Launcher v1.0                █
echo  █                                                      █
echo  ████████████████████████████████████████████████████████
echo.

REM Try different Python commands
set PYTHON_FOUND=0

echo 🔍 Searching for Python installation...

REM Try python command
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python
    set PYTHON_FOUND=1
    goto :found_python
)

REM Try python3 command
python3 --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python3
    set PYTHON_FOUND=1
    goto :found_python
)

REM Try py command (Python Launcher)
py --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=py
    set PYTHON_FOUND=1
    goto :found_python
)

REM Python not found
:python_not_found
echo.
echo ❌ Python not found on this system!
echo.
echo 💡 To fix this issue:
echo    1. Download Python from: https://python.org/downloads
echo    2. During installation, CHECK "Add Python to PATH"
echo    3. Restart your computer after installation
echo    4. Run this file again
echo.
echo 🔧 Alternative: If Python is installed but not in PATH:
echo    1. Search for "Environment Variables" in Windows
echo    2. Add Python installation folder to PATH
echo    3. Common locations:
echo       - C:\Python39\
echo       - C:\Users\<USER>\AppData\Local\Programs\Python\
echo.
pause
exit /b 1

:found_python
echo ✅ Found Python: %PYTHON_CMD%
echo.

REM Show Python version
echo 📋 Python Information:
%PYTHON_CMD% --version
echo.

REM Check if launcher exists
if exist launcher.py (
    echo 🚀 Starting universal launcher...
    %PYTHON_CMD% launcher.py
) else (
    echo 🚀 Starting application directly...
    if exist start_app.py (
        %PYTHON_CMD% start_app.py
    ) else if exist app.py (
        %PYTHON_CMD% app.py
    ) else (
        echo ❌ Application files not found!
        echo 💡 Make sure you're in the correct directory
        pause
        exit /b 1
    )
)

echo.
echo 👋 Thank you for using ATE MEG's FROZEN FOODS POS System!
pause
