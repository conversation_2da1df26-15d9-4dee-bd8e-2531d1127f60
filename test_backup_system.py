#!/usr/bin/env python3
"""
Test the backup system by creating a sample transaction
"""

import requests
import json

def test_backup_system():
    """Test the backup system with a sample transaction"""
    print("🧪 Testing Backup System...")
    
    # Test data for a transaction
    test_transaction = {
        "cart_items": [
            {
                "id": 1,
                "name": "Chicken Pastil",
                "price": 110.0,
                "quantity": 1
            }
        ],
        "payment_method": "cash",
        "customer_id": None,
        "cashier_id": 1
    }
    
    try:
        # Process a test sale
        print("💰 Processing test transaction...")
        response = requests.post(
            'http://localhost:5000/process_sale',
            headers={'Content-Type': 'application/json'},
            data=json.dumps(test_transaction)
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ Transaction successful: {data.get('transaction_number')}")
                print(f"   Amount: ₱{data.get('total_amount'):.2f}")
                print("   📁 Automatic CSV backup should have been created!")
            else:
                print(f"❌ Transaction failed: {data.get('message')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        print("💡 Make sure the Flask app is running on localhost:5000")
    
    # Test full backup API
    print("\n📦 Testing full backup API...")
    try:
        response = requests.get('http://localhost:5000/api/backup/full')
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ Full backup successful!")
                print(f"   📂 Backup directory: {data.get('backup_directory')}")
            else:
                print(f"❌ Backup failed: {data.get('error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_backup_system()
