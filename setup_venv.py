#!/usr/bin/env python3
"""
Universal Virtual Environment Setup
Creates a fresh venv that works on any device
"""

import os
import sys
import subprocess
import shutil

def setup_virtual_environment():
    """Create a fresh virtual environment for the current device"""
    
    print("🔧 Setting up Virtual Environment for ATE MEG's POS System")
    print("=" * 60)
    
    # Remove existing venv if it exists
    venv_path = "venv"
    if os.path.exists(venv_path):
        print("🗑️  Removing existing virtual environment...")
        try:
            shutil.rmtree(venv_path)
            print("✅ Old virtual environment removed")
        except Exception as e:
            print(f"⚠️  Could not remove old venv: {e}")
            print("💡 You may need to delete the 'venv' folder manually")
            return False
    
    # Create new virtual environment
    print("🔨 Creating new virtual environment...")
    try:
        subprocess.run([sys.executable, "-m", "venv", venv_path], check=True)
        print("✅ Virtual environment created successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to create virtual environment: {e}")
        print("💡 Make sure you have Python 3.7+ installed")
        return False
    
    # Determine the correct pip path
    if os.name == 'nt':  # Windows
        pip_path = os.path.join(venv_path, "Scripts", "pip")
        python_path = os.path.join(venv_path, "Scripts", "python")
    else:  # Unix/Linux/macOS
        pip_path = os.path.join(venv_path, "bin", "pip")
        python_path = os.path.join(venv_path, "bin", "python")
    
    # Install requirements
    print("📦 Installing required packages...")
    try:
        subprocess.run([pip_path, "install", "-r", "requirements.txt"], check=True)
        print("✅ All packages installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install packages: {e}")
        print("💡 Try running: pip install flask flask-sqlalchemy pandas")
        return False
    
    # Create activation scripts
    create_activation_scripts(venv_path)
    
    print("\n🎉 Virtual Environment Setup Complete!")
    print("=" * 60)
    print("📋 Next Steps:")
    print("1. Run 'activate_venv.bat' (Windows) or 'source activate_venv.sh' (Mac/Linux)")
    print("2. Then run 'python start_app.py'")
    print("3. Or just use the universal launchers (RUN_POS_SYSTEM.bat)")
    print("\n🌟 The system is now ready to run on this device!")
    
    return True

def create_activation_scripts(venv_path):
    """Create platform-specific activation scripts"""
    
    # Windows activation script
    windows_script = f"""@echo off
echo 🔧 Activating Virtual Environment...
call {venv_path}\\Scripts\\activate.bat
echo ✅ Virtual environment activated
echo 💡 You can now run: python start_app.py
echo 💡 To deactivate, type: deactivate
cmd /k
"""
    
    with open("activate_venv.bat", "w") as f:
        f.write(windows_script)
    
    # Unix/Linux/macOS activation script
    unix_script = f"""#!/bin/bash
echo "🔧 Activating Virtual Environment..."
source {venv_path}/bin/activate
echo "✅ Virtual environment activated"
echo "💡 You can now run: python start_app.py"
echo "💡 To deactivate, type: deactivate"
bash
"""
    
    with open("activate_venv.sh", "w") as f:
        f.write(unix_script)
    
    # Make the shell script executable on Unix systems
    if os.name != 'nt':
        os.chmod("activate_venv.sh", 0o755)
    
    print("✅ Activation scripts created")

if __name__ == "__main__":
    success = setup_venv()
    if not success:
        input("Press Enter to exit...")
        sys.exit(1)
