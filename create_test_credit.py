#!/usr/bin/env python3
"""
Create test credit transactions for testing payment functionality
"""

from app import app, db, Customer, CreditTransaction, Product, Transaction, TransactionItem
from datetime import datetime, timezone
import random

def create_test_credit_transactions():
    """Create test credit transactions for existing customers"""
    
    with app.app_context():
        print("🧪 Creating Test Credit Transactions...")
        print("=" * 50)
        
        # Get existing customers
        customers = Customer.query.all()
        if not customers:
            print("❌ No customers found! Please add customers first.")
            return
            
        # Get some products for creating realistic transactions
        products = Product.query.filter(Product.stock > 0).limit(3).all()
        if not products:
            print("❌ No products with stock found! Please add products first.")
            return
            
        print(f"📋 Found {len(customers)} customers and {len(products)} products")
        print()
        
        # Create test credit transactions for first 2-3 customers
        test_customers = customers[:3]  # Take first 3 customers
        
        for i, customer in enumerate(test_customers):
            # Create a mock transaction first
            transaction_number = f"TEST-{datetime.now().strftime('%Y%m%d')}-{i+1:03d}"
            
            # Create transaction record
            transaction = Transaction(
                transaction_number=transaction_number,
                total_amount=0  # Will update after calculating items
            )
            db.session.add(transaction)
            db.session.flush()  # Get the transaction ID
            
            # Add some random products to the transaction
            total_amount = 0
            num_items = random.randint(1, 3)  # 1-3 different products
            
            for _ in range(num_items):
                product = random.choice(products)
                quantity = random.randint(1, 3)  # 1-3 quantity
                unit_price = product.price
                item_total = quantity * unit_price
                total_amount += item_total
                
                # Create transaction item
                transaction_item = TransactionItem(
                    transaction_id=transaction.id,
                    product_id=product.id,
                    quantity=quantity,
                    unit_price=unit_price,
                    total_price=item_total
                )
                db.session.add(transaction_item)
                
                print(f"  📦 {product.name} x{quantity} = ₱{item_total:.2f}")
            
            # Update transaction total
            transaction.total_amount = total_amount
            
            # Create credit transaction
            credit_amount = total_amount
            credit_transaction = CreditTransaction(
                customer_id=customer.id,
                transaction_id=transaction.id,
                amount=credit_amount,
                transaction_type='credit',
                description=f'Test credit purchase - {transaction_number}',
                is_paid=False
            )
            db.session.add(credit_transaction)
            
            # Update customer total credit
            customer.total_credit += credit_amount
            customer.updated_at = datetime.now(timezone.utc)
            
            print(f"💳 Created credit transaction for {customer.name}")
            print(f"   Amount: ₱{credit_amount:.2f}")
            print(f"   Transaction: {transaction_number}")
            print()
        
        # Commit all changes
        db.session.commit()
        
        print("✅ Test credit transactions created successfully!")
        print()
        print("📊 Summary:")
        print("-" * 30)
        
        # Show updated customer balances
        updated_customers = Customer.query.filter(Customer.total_credit > 0).all()
        for customer in updated_customers:
            print(f"{customer.name:<20} Credit: ₱{customer.total_credit:.2f} | Paid: ₱{customer.total_paid:.2f} | Balance: ₱{customer.balance:.2f}")
        
        print()
        print("🎯 Next Steps:")
        print("1. Go to Credit Management: http://localhost:5000/credit_management")
        print("2. You should now see customers with outstanding debt")
        print("3. Click the '💲 Pay' button to test payment recording")
        print("4. Enter payment amounts and test the functionality")

def create_partial_payments():
    """Create some partial payments for testing"""
    
    with app.app_context():
        print("\n💰 Creating some partial payments for testing...")
        
        # Get customers with credit
        customers_with_credit = Customer.query.filter(Customer.total_credit > 0).all()
        
        if not customers_with_credit:
            print("❌ No customers with credit found!")
            return
            
        # Add partial payments for some customers
        for customer in customers_with_credit[:2]:  # First 2 customers
            if customer.balance > 0:
                # Pay 30-70% of their balance
                payment_percentage = random.uniform(0.3, 0.7)
                payment_amount = round(customer.balance * payment_percentage, 2)
                
                # Create payment transaction
                payment_transaction = CreditTransaction(
                    customer_id=customer.id,
                    amount=payment_amount,
                    transaction_type='payment',
                    description=f'Test partial payment',
                    is_paid=True
                )
                db.session.add(payment_transaction)
                
                # Update customer total paid
                customer.total_paid += payment_amount
                customer.updated_at = datetime.now(timezone.utc)
                
                print(f"💵 {customer.name} paid ₱{payment_amount:.2f} (partial payment)")
        
        db.session.commit()
        print("✅ Partial payments created!")

if __name__ == "__main__":
    print("🧪 ATE MEG's FROZEN FOODS - Test Credit Creator")
    print("=" * 60)
    
    try:
        # Create test credit transactions
        create_test_credit_transactions()
        
        # Ask if user wants partial payments too
        print("\n" + "="*50)
        response = input("Create some partial payments too? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            create_partial_payments()
        
        print("\n🎉 Test data creation completed!")
        print("Now you can test the payment functionality in the Credit Management page.")
        
    except Exception as e:
        print(f"❌ Error creating test data: {e}")
        import traceback
        traceback.print_exc()
