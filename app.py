
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, timezone, date, timedelta
import os
import csv
import pandas as pd

app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY') or 'your-secret-key-here'

# Custom Jinja2 filters for number formatting
@app.template_filter('currency')
def currency_filter(value):
    """Format number as currency with commas"""
    if value is None:
        return "₱0.00"
    try:
        return f"₱{float(value):,.2f}"
    except (ValueError, TypeError):
        return "₱0.00"

@app.template_filter('number')
def number_filter(value):
    """Format number with commas"""
    if value is None:
        return "0"
    try:
        if isinstance(value, float) and value.is_integer():
            return f"{int(value):,}"
        return f"{float(value):,.2f}"
    except (ValueError, TypeError):
        return "0"
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL') or 'sqlite:///frozen_foods_pos.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Ensure instance folder exists
if not os.path.exists('instance'):
    os.makedirs('instance')

db = SQLAlchemy(app)

# Add custom Jinja2 filters
@app.template_filter('stock')
def stock_filter(products):
    """Filter products by stock level"""
    return [p for p in products if p.stock > 0]

@app.template_filter('low_stock')
def low_stock_filter(products, threshold=5):
    """Filter products with low stock"""
    return [p for p in products if p.stock <= threshold]

@app.template_filter('out_of_stock')
def out_of_stock_filter(products):
    """Filter products that are out of stock"""
    return [p for p in products if p.stock == 0]

# Database Models
class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    price = db.Column(db.Float, nullable=False, default=0.0)  # Selling price
    capital_ratio = db.Column(db.Float, nullable=False, default=0.7)  # Capital price ratio (default 70%)
    stock = db.Column(db.Integer, nullable=False, default=0)
    received = db.Column(db.Integer, nullable=False, default=0)
    sold = db.Column(db.Integer, nullable=False, default=0)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    @property
    def capital_price(self):
        """Calculate capital price based on selling price and ratio"""
        return round(self.price * self.capital_ratio, 2)

    def set_capital_price(self, capital_price):
        """Set capital price by calculating the ratio"""
        if self.price > 0:
            self.capital_ratio = capital_price / self.price
        else:
            self.capital_ratio = 0.7

    @property
    def profit_per_unit(self):
        """Calculate profit per unit (selling price - capital price)"""
        return round(self.price - self.capital_price, 2)

    @property
    def total_profit(self):
        """Calculate total profit from sold units"""
        return round(self.sold * self.profit_per_unit, 2)

    @property
    def total_revenue(self):
        """Calculate total revenue from sold units"""
        return round(self.sold * self.price, 2)

    @property
    def total_capital_invested(self):
        """Calculate total capital invested in received stock"""
        return round(self.received * self.capital_price, 2)

    def __repr__(self):
        return f'<Product {self.name}>'

class Sale(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    transaction_id = db.Column(db.Integer, db.ForeignKey('transaction.id'), nullable=True)  # Added for robust linking
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)
    sale_date = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    product = db.relationship('Product', backref=db.backref('sales', lazy=True))
    transaction = db.relationship('Transaction', backref=db.backref('sales', lazy=True))

    def __repr__(self):
        return f'<Sale {self.product.name} - {self.quantity}>'

class Cashier(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    is_active = db.Column(db.Boolean, nullable=False, default=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f'<Cashier {self.name}>'

class Transaction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    transaction_number = db.Column(db.String(20), unique=True, nullable=False)
    total_amount = db.Column(db.Float, nullable=False)
    cashier_id = db.Column(db.Integer, db.ForeignKey('cashier.id'), nullable=True)
    transaction_date = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    payment_method = db.Column(db.String(10), default='cash')  # 'cash' or 'credit'
    customer_type = db.Column(db.String(10), default='default')  # 'default' or 'reseller'

    cashier = db.relationship('Cashier', backref=db.backref('transactions', lazy=True))

    def __repr__(self):
        return f'<Transaction {self.transaction_number}>'

class TransactionItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    transaction_id = db.Column(db.Integer, db.ForeignKey('transaction.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)

    transaction = db.relationship('Transaction', backref=db.backref('items', lazy=True))
    product = db.relationship('Product', backref=db.backref('transaction_items', lazy=True))

class ProductTransaction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    transaction_type = db.Column(db.String(20), nullable=False)  # 'stock_add', 'stock_remove', 'price_change', 'sale'
    quantity_change = db.Column(db.Integer, nullable=True)  # For stock changes
    old_price = db.Column(db.Float, nullable=True)  # For price changes
    new_price = db.Column(db.Float, nullable=True)  # For price changes
    old_capital_price = db.Column(db.Float, nullable=True)  # For capital price changes
    new_capital_price = db.Column(db.Float, nullable=True)  # For capital price changes
    reason = db.Column(db.String(100), nullable=True)
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    product = db.relationship('Product', backref=db.backref('transactions', lazy=True))

    def __repr__(self):
        return f'<ProductTransaction {self.transaction_type} for {self.product.name}>'

class Customer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), nullable=True)
    address = db.Column(db.Text, nullable=True)
    total_credit = db.Column(db.Float, nullable=False, default=0.0)
    total_paid = db.Column(db.Float, nullable=False, default=0.0)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f'<Customer {self.name}>'

    @property
    def balance(self):
        return self.total_credit - self.total_paid

class CreditTransaction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=False)
    transaction_id = db.Column(db.Integer, db.ForeignKey('transaction.id'), nullable=True)
    amount = db.Column(db.Float, nullable=False)
    transaction_type = db.Column(db.String(20), nullable=False)  # 'credit' or 'payment'
    description = db.Column(db.Text, nullable=True)
    is_paid = db.Column(db.Boolean, nullable=False, default=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    customer = db.relationship('Customer', backref=db.backref('credit_transactions', lazy=True))
    transaction = db.relationship('Transaction', backref=db.backref('credit_transaction', uselist=False))

class Settings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(50), unique=True, nullable=False)
    value = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f'<Settings {self.key}: {self.value}>'

    @staticmethod
    def get_reseller_discount():
        """Get the current reseller discount amount"""
        setting = Settings.query.filter_by(key='reseller_discount').first()
        return float(setting.value) if setting else 5.0  # Default 5 pesos

    @staticmethod
    def set_reseller_discount(amount):
        """Set the reseller discount amount"""
        setting = Settings.query.filter_by(key='reseller_discount').first()
        if setting:
            setting.value = str(amount)
            setting.updated_at = datetime.now(timezone.utc)
        else:
            setting = Settings(
                key='reseller_discount',
                value=str(amount),
                description='Discount amount in pesos for reseller customers'
            )
            db.session.add(setting)
        db.session.commit()
        return setting

    def __repr__(self):
        return f'<CreditTransaction {self.customer.name} - {self.amount}>'

# Routes
@app.route('/')
def index():
    from datetime import datetime, date

    # Get dashboard statistics
    total_products = Product.query.count()

    # Today's date for filtering
    today = date.today()
    current_date = today.strftime('%B %d, %Y')

    # Calculate cash revenue (immediate cash sales)
    cash_revenue = db.session.query(db.func.sum(Sale.total_price)).join(Transaction).filter(
        Transaction.payment_method == 'cash'
    ).scalar() or 0

    # Calculate total payments received from customers (this becomes revenue when paid)
    total_payments_received = db.session.query(db.func.sum(Customer.total_paid)).scalar() or 0

    # Calculate total credit sales (all credit sales ever made)
    total_credit_sales_ever = db.session.query(db.func.sum(Sale.total_price)).join(Transaction).filter(
        Transaction.payment_method == 'credit'
    ).scalar() or 0

    # CORRECTED: Calculate actual pending credit sales (credit sales - payments received)
    # This represents the true amount still owed by customers
    total_credit_sales = total_credit_sales_ever - total_payments_received

    # Calculate outstanding credit (total credit - total paid)
    total_outstanding_credit = db.session.query(
        db.func.sum(Customer.total_credit - Customer.total_paid)
    ).filter(Customer.total_credit > Customer.total_paid).scalar() or 0

    # CORRECTED: Total actual revenue = Cash sales + Customer payments received
    # This represents money actually received by the business
    total_actual_revenue = cash_revenue + total_payments_received

    # Today's performance metrics - CORRECTED to match total revenue logic
    # Today's cash sales (immediate revenue)
    today_cash_revenue = db.session.query(db.func.sum(Sale.total_price)).join(Transaction).filter(
        db.func.date(Transaction.transaction_date) == today,
        Transaction.payment_method == 'cash'
    ).scalar() or 0

    # Today's credit payments received (revenue from debt collection)
    today_credit_payments = db.session.query(db.func.sum(CreditTransaction.amount)).filter(
        db.func.date(CreditTransaction.created_at) == today,
        CreditTransaction.transaction_type == 'payment'
    ).scalar() or 0

    # CORRECTED: Today's actual revenue = cash sales + credit payments received today
    today_revenue = today_cash_revenue + today_credit_payments

    today_transactions = Transaction.query.filter(
        db.func.date(Transaction.transaction_date) == today
    ).count()

    today_items_sold = db.session.query(db.func.sum(Sale.quantity)).join(Transaction).filter(
        db.func.date(Transaction.transaction_date) == today
    ).scalar() or 0

    # Stock analysis
    low_stock_products = Product.query.filter(Product.stock <= 5, Product.stock > 0).all()
    out_of_stock_products = Product.query.filter(Product.stock == 0).all()
    low_stock_count = len(low_stock_products)
    out_of_stock_count = len(out_of_stock_products)

    # Customer debt analysis
    customers_with_debt = Customer.query.filter(Customer.total_credit > Customer.total_paid).count()

    # Total business metrics
    total_transactions = Transaction.query.count()
    # CORRECTED: Use actual revenue (money received) instead of including unpaid credit
    total_revenue = total_actual_revenue

    # Calculate total profit using actual transaction data (includes reseller discounts)
    total_profit = 0
    all_transaction_items = TransactionItem.query.join(Product).all()
    for item in all_transaction_items:
        if item.product:
            profit_per_unit = item.unit_price - item.product.capital_price
            total_profit += profit_per_unit * item.quantity

    # Top products today (actual today's sales)
    today_sales_data = db.session.query(
        Product.id,
        Product.name,
        Product.price,
        db.func.sum(Sale.quantity).label('sold_today'),
        db.func.sum(Sale.total_price).label('revenue_today')
    ).join(Sale).join(Transaction).filter(
        db.func.date(Transaction.transaction_date) == today
    ).group_by(Product.id, Product.name, Product.price).order_by(
        db.func.sum(Sale.quantity).desc()
    ).limit(5).all()

    # Convert to list of dictionaries for easier template access
    top_products_today = []
    for item in today_sales_data:
        top_products_today.append({
            'id': item.id,
            'name': item.name,
            'price': item.price,
            'sold_today': item.sold_today,
            'revenue_today': item.revenue_today
        })

    return render_template('index.html',
                        current_date=current_date,
                        total_products=total_products,
                        cash_revenue=cash_revenue,
                        total_credit_sales=total_credit_sales,
                        total_credit_sales_ever=total_credit_sales_ever,
                        total_payments_received=total_payments_received,
                        total_outstanding_credit=total_outstanding_credit,
                        today_revenue=today_revenue,
                        today_cash_revenue=today_cash_revenue,
                        today_credit_payments=today_credit_payments,
                        today_transactions=today_transactions,
                        today_items_sold=today_items_sold,
                        low_stock_products=low_stock_products,
                        low_stock_count=low_stock_count,
                        out_of_stock_count=out_of_stock_count,
                        customers_with_debt=customers_with_debt,
                        total_transactions=total_transactions,
                        total_revenue=total_revenue,
                        total_profit=total_profit,
                        top_products_today=top_products_today,
                        total_actual_revenue=total_actual_revenue)

@app.route('/products')
def products():
    products = Product.query.order_by(Product.name.asc()).all()
    return render_template('products.html', products=products)

@app.route('/add_product', methods=['GET', 'POST'])
def add_product():
    if request.method == 'POST':
        name = request.form['name']
        price = float(request.form.get('price', 0))
        received = int(request.form.get('received', 0))

        product = Product(name=name, price=price, received=received, stock=received)
        db.session.add(product)
        db.session.commit()

        # Backup to CSV after product addition
        backup_all_tables_on_change()

        flash('Product added successfully!', 'success')
        return redirect(url_for('products'))

    return render_template('add_product.html')

@app.route('/edit_product/<int:id>', methods=['GET', 'POST'])
def edit_product(id):
    product = Product.query.get_or_404(id)

    if request.method == 'POST':
        try:
            # Update basic product information
            old_name = product.name
            old_price = product.price
            old_capital_price = product.capital_price

            product.name = request.form['name']
            new_price = float(request.form.get('price', 0))  # Selling price
            new_capital_price = float(request.form.get('capital_price', product.capital_price))

            # Track price changes
            if new_price != old_price or new_capital_price != old_capital_price:
                price_transaction = ProductTransaction(
                    product_id=product.id,
                    transaction_type='price_change',
                    old_price=old_price,
                    new_price=new_price,
                    old_capital_price=old_capital_price,
                    new_capital_price=new_capital_price,
                    notes=f'Price changed from ₱{old_price:.2f} to ₱{new_price:.2f}, Capital from ₱{old_capital_price:.2f} to ₱{new_capital_price:.2f}'
                )
                db.session.add(price_transaction)

            product.price = new_price
            product.set_capital_price(new_capital_price)

            # Handle stock adjustment (new system)
            stock_adjustment = int(request.form.get('stock_adjustment', 0))
            adjustment_reason = request.form.get('adjustment_reason', '')

            if stock_adjustment != 0:
                # Check if adjustment would result in negative stock
                new_stock = product.stock + stock_adjustment
                if new_stock < 0:
                    flash(f'Cannot adjust stock by {stock_adjustment}. This would result in negative stock ({new_stock}).', 'danger')
                    return render_template('edit_product.html', product=product)

                # Apply stock adjustment
                old_stock = product.stock
                product.stock = new_stock

                # Update received quantity to reflect net stock movements
                # This makes "received" show the net total of all stock adjustments
                product.received += stock_adjustment

                if stock_adjustment > 0:
                    transaction_type = 'stock_add'
                else:
                    transaction_type = 'stock_remove'

                # Log the transaction
                transaction = ProductTransaction(
                    product_id=product.id,
                    transaction_type=transaction_type,
                    quantity_change=stock_adjustment,
                    reason=adjustment_reason if adjustment_reason else None,
                    notes=f'Stock changed from {old_stock} to {new_stock}'
                )
                db.session.add(transaction)

                # Log the adjustment for tracking
                flash_message = f'Stock adjusted by {stock_adjustment:+d} units'
                if adjustment_reason:
                    flash_message += f' (Reason: {adjustment_reason.replace("_", " ").title()})'
                flash_message += f'. New stock: {product.stock}'

                flash(flash_message, 'info')

            product.updated_at = datetime.now(timezone.utc)
            db.session.commit()

            # Backup to CSV after product update
            backup_all_tables_on_change()

            flash('Product updated successfully!', 'success')
            return redirect(url_for('products'))

        except ValueError as e:
            flash('Invalid input values. Please check your entries.', 'danger')
        except Exception as e:
            flash(f'Error updating product: {str(e)}', 'danger')

    return render_template('edit_product.html', product=product)

@app.route('/product_history/<int:id>')
def product_history(id):
    product = Product.query.get_or_404(id)

    # Get all transactions for this product, ordered by most recent first
    transactions = ProductTransaction.query.filter_by(product_id=id).order_by(ProductTransaction.created_at.desc()).all()

    # Also get sales history for this product
    sales = Sale.query.filter_by(product_id=id).order_by(Sale.sale_date.desc()).limit(50).all()

    return render_template('product_history.html', product=product, transactions=transactions, sales=sales)

# @app.route('/update_prices/<int:id>', methods=['POST'])
# def update_prices(id):
#     """Quick update for selling and capital prices - DISABLED for security"""
#     # Prices can only be edited through the dedicated edit product page
#     pass

@app.route('/delete_product/<int:id>')
def delete_product(id):
    product = Product.query.get_or_404(id)
    db.session.delete(product)
    db.session.commit()
    flash('Product deleted successfully!', 'success')
    return redirect(url_for('products'))

@app.route('/pos')
def pos():
    # Show all products, including out of stock, sorted alphabetically
    products = Product.query.order_by(Product.name.asc()).all()
    customers = Customer.query.order_by(Customer.name.asc()).all()
    reseller_discount = Settings.get_reseller_discount()
    return render_template('pos.html', products=products, customers=customers, reseller_discount=reseller_discount)

@app.route('/process_sale', methods=['POST'])
def process_sale():
    try:
        cart_items = request.json.get('cart_items', [])
        customer_id = request.json.get('customer_id')
        payment_method = request.json.get('payment_method', 'cash')
        customer_type = request.json.get('customer_type', 'default')
        cashier_id = request.json.get('cashier_id')

        if not cart_items:
            return jsonify({'success': False, 'message': 'Cart is empty', 'error_type': 'validation'})

        # Validate customer for credit sales
        if payment_method == 'credit' and not customer_id:
            return jsonify({'success': False, 'message': 'Customer must be selected for credit sales', 'error_type': 'validation'})

        # Validate cashier
        if not cashier_id:
            return jsonify({'success': False, 'message': 'Please select a cashier', 'error_type': 'validation'})

        # Generate transaction number
        transaction_number = f"TXN{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # Calculate total
        total_amount = 0
        transaction_items = []

        for item in cart_items:
            product = Product.query.get(item['product_id'])
            if not product:
                return jsonify({'success': False, 'message': 'Product not found', 'error_type': 'validation'})

            if product.stock < item['quantity']:
                return jsonify({'success': False, 'message': f'Insufficient stock for {product.name}. Available: {product.stock}', 'error_type': 'stock'})

            unit_price = product.price

            # Apply reseller discount if customer type is reseller
            if customer_type == 'reseller':
                reseller_discount = Settings.get_reseller_discount()
                unit_price = max(0, unit_price - reseller_discount)  # Ensure price doesn't go negative

            item_total = item['quantity'] * unit_price
            total_amount += item_total

            transaction_items.append({
                'product': product,
                'quantity': item['quantity'],
                'unit_price': unit_price,
                'total_price': item_total
            })

        # Create transaction
        transaction = Transaction(
            transaction_number=transaction_number,
            total_amount=total_amount,
            cashier_id=cashier_id,
            payment_method=payment_method,
            customer_type=customer_type
        )
        db.session.add(transaction)
        db.session.flush()  # Get transaction ID

        # Process each item
        for item_data in transaction_items:
            product = item_data['product']
            quantity = item_data['quantity']

            # Update product stock and sold count
            product.stock -= quantity
            product.sold += quantity

            # Create transaction item
            transaction_item = TransactionItem(
                transaction_id=transaction.id,
                product_id=product.id,
                quantity=quantity,
                unit_price=item_data['unit_price'],
                total_price=item_data['total_price']
            )
            db.session.add(transaction_item)

            # Create sale record with transaction link
            sale = Sale(
                product_id=product.id,
                transaction_id=transaction.id,
                quantity=quantity,
                unit_price=item_data['unit_price'],
                total_price=item_data['total_price']
            )
            db.session.add(sale)

        # Handle credit transaction
        if payment_method == 'credit' and customer_id:
            customer = Customer.query.get(customer_id)
            if customer:
                # Create credit transaction
                credit_transaction = CreditTransaction(
                    customer_id=customer_id,
                    transaction_id=transaction.id,
                    amount=total_amount,
                    transaction_type='credit',
                    description=f'Purchase on credit - {transaction_number}'
                )
                db.session.add(credit_transaction)

                # Update customer total credit
                customer.total_credit += total_amount
                customer.updated_at = datetime.now(timezone.utc)

        # Commit all changes
        db.session.commit()

        # Backup transaction to CSV
        backup_transaction_to_csv(transaction.id)

        response_data = {
            'success': True,
            'message': 'Sale completed successfully',
            'transaction_number': transaction_number,
            'total_amount': total_amount,
            'payment_method': payment_method
        }

        if payment_method == 'credit' and customer_id:
            customer = Customer.query.get(customer_id)
            response_data['customer_name'] = customer.name
            response_data['message'] = f'Sale completed successfully for {customer.name}'

        print(f"Returning JSON response: {response_data}")
        return jsonify(response_data)

    except Exception as e:
        # Rollback any changes if there's an error
        db.session.rollback()

        # Log the error for debugging
        print(f"Error processing sale: {str(e)}")

        return jsonify({
            'success': False,
            'message': 'Sale processing failed. Please try again.',
            'error_type': 'server_error'
        }), 500

@app.route('/reports')
def reports():
    return render_template('reports.html')

@app.route('/sales_report')
def sales_report():
    try:
        # Get sales data grouped by product (CASH ONLY - exclude credit sales)
        sales_data = db.session.query(
            Product.name,
            db.func.sum(Sale.quantity).label('total_sold'),
            db.func.sum(Sale.total_price).label('total_revenue')
        ).join(Sale).join(Transaction).filter(
            Transaction.payment_method == 'cash'
        ).group_by(Product.id, Product.name).all()

        # Get daily cash sales (immediate revenue)
        daily_cash_sales = db.session.query(
            db.func.date(Sale.sale_date).label('sale_date'),
            db.func.sum(Sale.total_price).label('daily_cash_revenue'),
            db.func.sum(Sale.quantity).label('daily_quantity')
        ).join(Transaction).filter(
            Transaction.payment_method == 'cash'
        ).group_by(db.func.date(Sale.sale_date)).order_by(db.func.date(Sale.sale_date).desc()).limit(30).all()

        # Get daily credit payments (revenue from debt collection)
        daily_credit_payments = db.session.query(
            db.func.date(CreditTransaction.created_at).label('payment_date'),
            db.func.sum(CreditTransaction.amount).label('daily_payment_revenue')
        ).filter(
            CreditTransaction.transaction_type == 'payment'
        ).group_by(db.func.date(CreditTransaction.created_at)).order_by(db.func.date(CreditTransaction.created_at).desc()).limit(30).all()

        # Get credit sales data separately
        credit_sales_data = db.session.query(
            Product.name,
            db.func.sum(Sale.quantity).label('total_sold'),
            db.func.sum(Sale.total_price).label('total_credit')
        ).join(Sale).join(Transaction).filter(
            Transaction.payment_method == 'credit'
        ).group_by(Product.id, Product.name).all()

        # Combine cash sales and credit payments into daily revenue
        daily_sales = []

        # Create a dictionary to combine cash sales and credit payments by date
        revenue_by_date = {}

        # Add cash sales revenue
        for row in daily_cash_sales:
            if isinstance(row.sale_date, str):
                sale_date = datetime.strptime(row.sale_date, '%Y-%m-%d').date()
            else:
                sale_date = row.sale_date
            revenue_by_date[sale_date] = {
                'sale_date': sale_date,
                'cash_revenue': row.daily_cash_revenue or 0,
                'payment_revenue': 0,
                'daily_quantity': row.daily_quantity or 0
            }

        # Add credit payment revenue
        for row in daily_credit_payments:
            if isinstance(row.payment_date, str):
                payment_date = datetime.strptime(row.payment_date, '%Y-%m-%d').date()
            else:
                payment_date = row.payment_date

            if payment_date in revenue_by_date:
                revenue_by_date[payment_date]['payment_revenue'] = row.daily_payment_revenue or 0
            else:
                revenue_by_date[payment_date] = {
                    'sale_date': payment_date,
                    'cash_revenue': 0,
                    'payment_revenue': row.daily_payment_revenue or 0,
                    'daily_quantity': 0
                }

        # Convert to list and calculate total daily revenue
        for date_data in revenue_by_date.values():
            date_data['daily_revenue'] = date_data['cash_revenue'] + date_data['payment_revenue']
            daily_sales.append(date_data)

        # Sort by date descending
        daily_sales.sort(key=lambda x: x['sale_date'], reverse=True)

        return render_template('sales_report.html',
                             sales_data=sales_data,
                             daily_sales=daily_sales,
                             credit_sales_data=credit_sales_data)

    except Exception as e:
        flash(f'Error loading sales report: {str(e)}', 'danger')
        return redirect(url_for('reports'))

@app.route('/inventory_report')
def inventory_report():
    try:
        # Get all products as a list
        products_query = Product.query.all()

        # Ensure we have a list, not a single object
        if not isinstance(products_query, list):
            products_query = [products_query] if products_query else []

        # Create a clean list of product data to avoid iteration issues
        products = []
        total_revenue = 0
        total_profit = 0
        total_stock_value = 0
        total_stock_units = 0
        low_stock_count = 0
        out_of_stock_count = 0

        for product in products_query:
            # Calculate individual product metrics using actual transaction data
            capital_price = product.capital_price

            # Calculate actual profit and revenue from transactions
            product_items = TransactionItem.query.filter_by(product_id=product.id).all()
            total_product_profit = 0
            total_product_revenue = 0

            for item in product_items:
                item_revenue = item.unit_price * item.quantity
                item_profit = (item.unit_price - capital_price) * item.quantity
                total_product_revenue += item_revenue
                total_product_profit += item_profit

            # Average profit per unit (for display)
            avg_profit_per_unit = total_product_profit / product.sold if product.sold > 0 else (product.price - capital_price)

            stock_value = round(product.stock * product.price, 2)

            # Create clean product data
            product_data = {
                'id': product.id,
                'name': product.name,
                'price': product.price,
                'capital_price': capital_price,
                'profit_per_unit': round(avg_profit_per_unit, 2),
                'received': product.received,
                'sold': product.sold,
                'stock': product.stock,
                'stock_value': stock_value,
                'total_profit': round(total_product_profit, 2),
                'total_revenue': round(total_product_revenue, 2),
                'status': 'Out of Stock' if product.stock == 0 else 'Low Stock' if product.stock <= 5 else 'In Stock'
            }

            products.append(product_data)

            # Update totals
            total_revenue += total_product_revenue
            total_profit += total_product_profit
            total_stock_value += stock_value
            total_stock_units += product.stock

            if product.stock == 0:
                out_of_stock_count += 1
            elif product.stock <= 5:
                low_stock_count += 1

        # Create summary data
        summary = {
            'total_products': len(products),
            'total_stock_units': total_stock_units,
            'low_stock_count': low_stock_count,
            'out_of_stock_count': out_of_stock_count,
            'total_revenue': total_revenue,
            'total_profit': total_profit,
            'total_stock_value': total_stock_value
        }

        # Create separate lists for template compatibility
        low_stock_products = [p for p in products if p['stock'] <= 5 and p['stock'] > 0]
        out_of_stock_products = [p for p in products if p['stock'] == 0]

        return render_template('inventory_report.html',
                             products=products,
                             summary=summary,
                             total_products=summary['total_products'],
                             total_stock_units=summary['total_stock_units'],
                             low_stock_products=low_stock_products,
                             out_of_stock_products=out_of_stock_products)

    except Exception as e:
        flash(f'Error loading inventory report: {str(e)}', 'danger')
        return redirect(url_for('reports'))

@app.route('/transaction_history')
def transaction_history():
    try:
        transactions = Transaction.query.order_by(Transaction.transaction_date.desc()).limit(100).all()
        today = date.today()

        return render_template('transaction_history.html', transactions=transactions, today=today)

    except Exception as e:
        flash(f'Error loading transaction history: {str(e)}', 'danger')
        return redirect(url_for('reports'))

@app.route('/sales_history')
def sales_history():
    """Sales History Management - Input and view individual transactions"""
    try:
        # Get recent transactions for display
        transactions = Transaction.query.order_by(Transaction.transaction_date.desc()).limit(50).all()

        # Get all products for the form
        products = Product.query.filter(Product.stock > 0).order_by(Product.name).all()

        # Get all active cashiers for the form
        cashiers = Cashier.query.filter_by(is_active=True).order_by(Cashier.name).all()

        # Get all customers for credit transactions
        customers = Customer.query.order_by(Customer.name).all()

        return render_template('sales_history.html',
                             transactions=transactions,
                             products=products,
                             cashiers=cashiers,
                             customers=customers)

    except Exception as e:
        flash(f'Error loading sales history: {str(e)}', 'danger')
        return redirect(url_for('reports'))

@app.route('/settings')
def settings():
    """Settings management page"""
    try:
        reseller_discount = Settings.get_reseller_discount()
        return render_template('settings.html', reseller_discount=reseller_discount)
    except Exception as e:
        flash(f'Error loading settings: {str(e)}', 'danger')
        return redirect(url_for('reports'))

@app.route('/api/update_reseller_discount', methods=['POST'])
def update_reseller_discount():
    """API endpoint to update reseller discount amount"""
    try:
        data = request.get_json()
        discount = float(data.get('discount', 5.0))

        if discount < 0:
            return jsonify({
                'success': False,
                'message': 'Discount cannot be negative'
            }), 400

        Settings.set_reseller_discount(discount)

        return jsonify({
            'success': True,
            'message': f'Reseller discount updated to ₱{discount:,.2f}',
            'discount': discount
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error updating discount: {str(e)}'
        }), 500

@app.route('/api/create_transaction', methods=['POST'])
def create_transaction():
    """API endpoint to create a new transaction from sales history form"""
    try:
        data = request.get_json()

        # Extract transaction data
        cashier_id = data.get('cashier_id')
        payment_method = data.get('payment_method', 'cash')
        customer_id = data.get('customer_id') if payment_method == 'credit' else None
        customer_type = data.get('customer_type', 'default')
        transaction_date_str = data.get('transaction_date')
        items = data.get('items', [])

        if not items:
            return jsonify({
                'success': False,
                'message': 'No items provided for transaction'
            }), 400

        # Parse transaction date (date only format)
        if transaction_date_str:
            # Parse date-only format (YYYY-MM-DD) and set time to current time
            date_part = datetime.strptime(transaction_date_str, '%Y-%m-%d').date()
            current_time = datetime.now(timezone.utc).time()
            transaction_date = datetime.combine(date_part, current_time).replace(tzinfo=timezone.utc)
        else:
            transaction_date = datetime.now(timezone.utc)

        # Generate unique transaction number
        transaction_number = f"TXN{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # Calculate total amount and prepare transaction items
        total_amount = 0
        transaction_items = []

        for item_data in items:
            product_id = item_data.get('product_id')
            quantity = int(item_data.get('quantity', 0))

            if quantity <= 0:
                continue

            product = Product.query.get(product_id)
            if not product:
                return jsonify({
                    'success': False,
                    'message': f'Product with ID {product_id} not found'
                }), 400

            # Check stock availability
            if product.stock < quantity:
                return jsonify({
                    'success': False,
                    'message': f'Insufficient stock for {product.name}. Available: {product.stock}, Requested: {quantity}'
                }), 400

            unit_price = product.price

            # Apply reseller discount if customer type is reseller
            if customer_type == 'reseller':
                reseller_discount = Settings.get_reseller_discount()
                unit_price = max(0, unit_price - reseller_discount)  # Ensure price doesn't go negative

            total_price = unit_price * quantity
            total_amount += total_price

            transaction_items.append({
                'product': product,
                'quantity': quantity,
                'unit_price': unit_price,
                'total_price': total_price
            })

        # Create transaction
        transaction = Transaction(
            transaction_number=transaction_number,
            total_amount=total_amount,
            cashier_id=cashier_id,
            transaction_date=transaction_date,
            payment_method=payment_method,
            customer_type=customer_type
        )
        db.session.add(transaction)
        db.session.flush()  # Get transaction ID

        # Process each item
        for item_data in transaction_items:
            product = item_data['product']
            quantity = item_data['quantity']

            # Update product stock and sold count
            product.stock -= quantity
            product.sold += quantity

            # Create transaction item
            transaction_item = TransactionItem(
                transaction_id=transaction.id,
                product_id=product.id,
                quantity=quantity,
                unit_price=item_data['unit_price'],
                total_price=item_data['total_price']
            )
            db.session.add(transaction_item)

            # Create sale record with transaction link
            sale = Sale(
                product_id=product.id,
                transaction_id=transaction.id,
                quantity=quantity,
                unit_price=item_data['unit_price'],
                total_price=item_data['total_price'],
                sale_date=transaction_date
            )
            db.session.add(sale)

        # Handle credit transaction
        if payment_method == 'credit' and customer_id:
            customer = Customer.query.get(customer_id)
            if customer:
                # Create credit transaction
                credit_transaction = CreditTransaction(
                    customer_id=customer_id,
                    transaction_id=transaction.id,
                    amount=total_amount,
                    transaction_type='credit',
                    description=f'Purchase on credit - {transaction_number}'
                )
                db.session.add(credit_transaction)

                # Update customer total credit
                customer.total_credit += total_amount
                customer.updated_at = datetime.now(timezone.utc)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'Transaction {transaction_number} created successfully',
            'transaction_number': transaction_number,
            'total_amount': total_amount
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Error creating transaction: {str(e)}'
        }), 500

@app.route('/api/get_reseller_discount')
def get_reseller_discount():
    """API endpoint to get current reseller discount amount"""
    try:
        discount = Settings.get_reseller_discount()
        return jsonify({
            'success': True,
            'discount': discount
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/delete_transaction/<transaction_number>', methods=['POST'])
def delete_transaction(transaction_number):
    try:
        # Find the transaction
        transaction = Transaction.query.filter_by(transaction_number=transaction_number).first()
        if not transaction:
            flash(f'Transaction {transaction_number} not found', 'danger')
            return redirect(url_for('transaction_history'))

        # Get all transaction items to restore stock
        transaction_items = TransactionItem.query.filter_by(transaction_id=transaction.id).all()

        # Restore product stock and reduce sold count
        for item in transaction_items:
            product = Product.query.get(item.product_id)
            if product:
                product.stock += item.quantity
                product.sold -= item.quantity

        # Delete related records in proper order
        # 1. Delete credit transactions if any
        CreditTransaction.query.filter_by(transaction_id=transaction.id).delete()

        # 2. Delete sales records - ROBUST: Direct transaction_id link
        Sale.query.filter_by(transaction_id=transaction.id).delete()

        # 3. Delete transaction items
        TransactionItem.query.filter_by(transaction_id=transaction.id).delete()

        # 4. Delete the transaction itself
        db.session.delete(transaction)

        # Commit all changes
        db.session.commit()

        flash(f'Transaction {transaction_number} deleted successfully and stock restored', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'Error deleting transaction: {str(e)}', 'danger')

    return redirect(url_for('transaction_history'))

@app.route('/api/product/<int:id>')
def get_product(id):
    product = Product.query.get_or_404(id)
    return jsonify({
        'id': product.id,
        'name': product.name,
        'price': product.price,
        'stock': product.stock
    })

@app.route('/api/credit_summary')
def credit_summary():
    # Calculate total outstanding credits
    total_outstanding = db.session.query(db.func.sum(Customer.total_credit - Customer.total_paid)).filter(
        Customer.total_credit > Customer.total_paid
    ).scalar() or 0

    # Calculate total paid
    total_paid = db.session.query(db.func.sum(Customer.total_paid)).scalar() or 0

    # Count customers with outstanding credit
    customers_with_credit = Customer.query.filter(Customer.total_credit > Customer.total_paid).count()

    return jsonify({
        'total_outstanding': total_outstanding,
        'total_paid': total_paid,
        'customers_with_credit': customers_with_credit
    })

@app.route('/api/cashiers')
def api_cashiers():
    """Get all active cashiers"""
    try:
        cashiers = Cashier.query.filter_by(is_active=True).order_by(Cashier.name).all()
        return jsonify([{
            'id': cashier.id,
            'name': cashier.name
        } for cashier in cashiers])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/cashiers', methods=['POST'])
def api_add_cashier():
    """Add a new cashier"""
    try:
        data = request.get_json()
        name = data.get('name', '').strip()

        if not name:
            return jsonify({'error': 'Cashier name is required'}), 400

        # Check if cashier already exists
        existing = Cashier.query.filter_by(name=name).first()
        if existing:
            return jsonify({'error': 'Cashier with this name already exists'}), 400

        cashier = Cashier(name=name)
        db.session.add(cashier)
        db.session.commit()

        # Backup to CSV after cashier addition
        backup_all_tables_on_change()

        return jsonify({
            'success': True,
            'cashier': {
                'id': cashier.id,
                'name': cashier.name
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/transactions/<int:transaction_id>/cashier', methods=['PUT'])
def update_transaction_cashier(transaction_id):
    """Update the cashier for a specific transaction"""
    try:
        data = request.get_json()
        cashier_id = data.get('cashier_id')

        if not cashier_id:
            return jsonify({'error': 'Cashier ID is required'}), 400

        # Verify cashier exists
        cashier = Cashier.query.get(cashier_id)
        if not cashier:
            return jsonify({'error': 'Cashier not found'}), 404

        # Update transaction
        transaction = Transaction.query.get_or_404(transaction_id)
        transaction.cashier_id = cashier_id
        db.session.commit()

        # Backup to CSV after transaction update
        backup_all_tables_on_change()

        return jsonify({
            'success': True,
            'message': f'Transaction cashier updated to {cashier.name}'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def backup_single_table_to_csv(table_name, data_list, backup_type="update"):
    """Backup a single table to CSV with operation type"""
    try:
        # Create backups directory
        os.makedirs('backups/live_updates', exist_ok=True)

        # Create timestamped backup file
        timestamp = datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')
        csv_file = f'backups/live_updates/{table_name}_{backup_type}_{timestamp}.csv'

        if data_list:
            # Convert to DataFrame and save
            import pandas as pd
            df = pd.DataFrame(data_list)
            df.to_csv(csv_file, index=False, encoding='utf-8')
            print(f"✅ {table_name} {backup_type} backed up to {csv_file}")

        # Also update the main table file
        main_csv_file = f'backups/live_updates/{table_name}_current.csv'
        if data_list:
            df = pd.DataFrame(data_list)
            df.to_csv(main_csv_file, index=False, encoding='utf-8')
            print(f"✅ {table_name} current state updated")

    except Exception as e:
        print(f"❌ Error backing up {table_name}: {str(e)}")

def backup_all_tables_on_change():
    """Backup all tables when any change occurs"""
    try:
        print("🔄 Database changed - updating CSV backups...")

        # Backup Products
        products = Product.query.all()
        product_data = []
        for p in products:
            product_data.append({
                'id': p.id, 'name': p.name, 'price': p.price,
                'capital_ratio': p.capital_ratio, 'stock': p.stock,
                'received': p.received, 'sold': p.sold,
                'total_revenue': p.total_revenue, 'total_profit': p.total_profit,
                'updated_at': p.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        backup_single_table_to_csv('products', product_data, 'live_update')

        # Backup Customers
        customers = Customer.query.all()
        customer_data = []
        for c in customers:
            customer_data.append({
                'id': c.id, 'name': c.name, 'phone': c.phone,
                'address': c.address, 'total_credit': c.total_credit,
                'total_paid': c.total_paid, 'balance': c.balance,
                'updated_at': c.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        backup_single_table_to_csv('customers', customer_data, 'live_update')

        # Backup Cashiers
        cashiers = Cashier.query.all()
        cashier_data = []
        for cashier in cashiers:
            cashier_data.append({
                'id': cashier.id, 'name': cashier.name,
                'is_active': cashier.is_active,
                'updated_at': cashier.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        backup_single_table_to_csv('cashiers', cashier_data, 'live_update')

        # Backup Transactions (recent ones)
        transactions = Transaction.query.order_by(Transaction.transaction_date.desc()).limit(100).all()
        transaction_data = []
        for t in transactions:
            cashier_name = t.cashier.name if t.cashier else "No Cashier"
            transaction_data.append({
                'id': t.id, 'transaction_number': t.transaction_number,
                'total_amount': t.total_amount, 'cashier_name': cashier_name,
                'payment_method': t.payment_method,
                'customer_type': getattr(t, 'customer_type', 'default'),
                'transaction_date': t.transaction_date.strftime('%Y-%m-%d %H:%M:%S')
            })
        backup_single_table_to_csv('transactions', transaction_data, 'live_update')

    except Exception as e:
        print(f"❌ Error in live backup: {str(e)}")

def backup_transaction_to_csv(transaction_id):
    """Backup a single transaction to CSV file"""
    try:
        # Create backups directory if it doesn't exist
        os.makedirs('backups/transactions', exist_ok=True)

        # Get transaction details
        transaction = Transaction.query.get(transaction_id)
        if not transaction:
            return

        # Prepare transaction data
        cashier_name = transaction.cashier.name if transaction.cashier else "No Cashier"
        transaction_data = {
            'timestamp': datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S'),
            'transaction_id': transaction.id,
            'transaction_number': transaction.transaction_number,
            'total_amount': transaction.total_amount,
            'cashier_id': transaction.cashier_id,
            'cashier_name': cashier_name,
            'payment_method': transaction.payment_method,
            'customer_type': getattr(transaction, 'customer_type', 'default'),
            'transaction_date': transaction.transaction_date.strftime('%Y-%m-%d %H:%M:%S')
        }

        # Get transaction items
        items = TransactionItem.query.filter_by(transaction_id=transaction_id).all()
        items_data = []
        for item in items:
            product_name = item.product.name if item.product else f"Product ID {item.product_id}"
            items_data.append({
                'product_id': item.product_id,
                'product_name': product_name,
                'quantity': item.quantity,
                'unit_price': item.unit_price,
                'total_price': item.total_price
            })

        # Save to daily transaction log
        date_str = transaction.transaction_date.strftime('%Y%m%d')
        csv_file = f'backups/transactions/transactions_{date_str}.csv'

        # Prepare CSV row
        csv_row = transaction_data.copy()
        csv_row['items'] = '; '.join([f"{item['quantity']}x {item['product_name']} @ ₱{item['unit_price']:,.2f}" for item in items_data])
        csv_row['items_count'] = len(items_data)

        # Write to CSV
        file_exists = os.path.isfile(csv_file)
        with open(csv_file, 'a', newline='', encoding='utf-8') as f:
            fieldnames = ['timestamp', 'transaction_id', 'transaction_number', 'total_amount',
                         'cashier_id', 'cashier_name', 'payment_method', 'customer_type',
                         'transaction_date', 'items_count', 'items']
            writer = csv.DictWriter(f, fieldnames=fieldnames)

            if not file_exists:
                writer.writeheader()
            writer.writerow(csv_row)

        print(f"✅ Transaction {transaction.transaction_number} backed up to {csv_file}")

    except Exception as e:
        print(f"❌ Error backing up transaction {transaction_id}: {str(e)}")

@app.route('/api/backup/full')
def api_full_backup():
    """API endpoint to trigger full database backup"""
    try:
        from backup_system import export_to_csv
        backup_dir = export_to_csv()
        return jsonify({
            'success': True,
            'message': 'Full backup completed successfully',
            'backup_directory': backup_dir
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/cashier_performance')
def api_cashier_performance():
    """API endpoint for cashier performance data"""
    try:
        cashiers = Cashier.query.filter_by(is_active=True).all()
        performance_data = []

        for cashier in cashiers:
            # Get all transactions for this cashier
            all_transactions = Transaction.query.filter_by(cashier_id=cashier.id).all()
            # Get only cash transactions for revenue calculation
            cash_transactions = Transaction.query.filter_by(cashier_id=cashier.id, payment_method='cash').all()
            # Get credit transactions separately
            credit_transactions = Transaction.query.filter_by(cashier_id=cashier.id, payment_method='credit').all()

            # Calculate cash revenue (immediate)
            cash_revenue = sum(t.total_amount for t in cash_transactions)
            # Calculate credit sales (not revenue until paid)
            total_credit_sales = sum(t.total_amount for t in credit_transactions)

            # Calculate payments received for this cashier's credit sales
            # Note: This is a simplified approach - in a more complex system,
            # you might want to track which payments relate to which cashier's sales
            cashier_credit_payments = 0  # For now, we'll track this separately

            # Total revenue = cash sales + any credit payments received
            total_sales = cash_revenue + cashier_credit_payments
            total_transactions = len(all_transactions)

            total_items_sold = 0
            total_profit = 0

            for transaction in all_transactions:
                items = TransactionItem.query.filter_by(transaction_id=transaction.id).all()
                for item in items:
                    total_items_sold += item.quantity
                    if item.product:
                        # Use actual selling price from transaction (includes reseller discounts)
                        profit_per_unit = item.unit_price - item.product.capital_price
                        total_profit += profit_per_unit * item.quantity

            performance_data.append({
                'cashier_id': cashier.id,
                'cashier_name': cashier.name,
                'total_sales': total_sales,  # Cash sales only
                'total_credit_sales': total_credit_sales,  # Credit sales separately
                'total_profit': total_profit,
                'total_transactions': total_transactions,
                'total_items_sold': total_items_sold,
                'avg_transaction_value': total_sales / len(cash_transactions) if cash_transactions else 0,
                'profit_margin': (total_profit / total_sales * 100) if total_sales > 0 else 0
            })

        # Sort by total sales
        performance_data.sort(key=lambda x: x['total_sales'], reverse=True)

        return jsonify({
            'success': True,
            'data': performance_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/debug/revenue_check')
def debug_revenue_check():
    """Debug route to check revenue discrepancy"""
    # Check Sale table total
    sale_total = db.session.query(db.func.sum(Sale.total_price)).scalar() or 0

    # Check Transaction table total
    transaction_total = db.session.query(db.func.sum(Transaction.total_amount)).scalar() or 0

    # Check TransactionItem table total
    transaction_item_total = db.session.query(db.func.sum(TransactionItem.total_price)).scalar() or 0

    # Count records
    sale_count = Sale.query.count()
    transaction_count = Transaction.query.count()
    transaction_item_count = TransactionItem.query.count()

    # Get recent records
    recent_sales = Sale.query.order_by(Sale.sale_date.desc()).limit(5).all()
    recent_transactions = Transaction.query.order_by(Transaction.transaction_date.desc()).limit(5).all()

    return jsonify({
        'totals': {
            'sale_table': sale_total,
            'transaction_table': transaction_total,
            'transaction_item_table': transaction_item_total,
            'discrepancy': abs(sale_total - transaction_total)
        },
        'counts': {
            'sales': sale_count,
            'transactions': transaction_count,
            'transaction_items': transaction_item_count
        },
        'recent_sales': [
            {
                'id': s.id,
                'total_price': s.total_price,
                'sale_date': s.sale_date.isoformat(),
                'product_id': s.product_id
            } for s in recent_sales
        ],
        'recent_transactions': [
            {
                'id': t.id,
                'transaction_number': t.transaction_number,
                'total_amount': t.total_amount,
                'transaction_date': t.transaction_date.isoformat()
            } for t in recent_transactions
        ]
    })

@app.route('/debug/fix_missing_sales')
def fix_missing_sales():
    """Fix missing Sale records by creating them from TransactionItems"""
    try:
        # Find TransactionItems that don't have corresponding Sale records
        missing_sales = []
        fixed_count = 0

        # Get all transaction items
        transaction_items = TransactionItem.query.all()

        for item in transaction_items:
            # Check if there's a corresponding sale record
            existing_sale = Sale.query.filter_by(
                product_id=item.product_id,
                quantity=item.quantity,
                unit_price=item.unit_price,
                total_price=item.total_price
            ).filter(
                db.func.abs(
                    db.func.julianday(Sale.sale_date) -
                    db.func.julianday(item.transaction.transaction_date)
                ) < 1  # Within 1 day
            ).first()

            if not existing_sale:
                # Create missing sale record with transaction link
                sale = Sale(
                    product_id=item.product_id,
                    transaction_id=item.transaction_id,
                    quantity=item.quantity,
                    unit_price=item.unit_price,
                    total_price=item.total_price,
                    sale_date=item.transaction.transaction_date
                )
                db.session.add(sale)
                missing_sales.append({
                    'transaction_number': item.transaction.transaction_number,
                    'product_id': item.product_id,
                    'quantity': item.quantity,
                    'total_price': item.total_price,
                    'date': item.transaction.transaction_date.isoformat()
                })
                fixed_count += 1

        # Commit the changes
        db.session.commit()

        # Recalculate totals
        new_sale_total = db.session.query(db.func.sum(Sale.total_price)).scalar() or 0
        transaction_total = db.session.query(db.func.sum(Transaction.total_amount)).scalar() or 0

        return jsonify({
            'success': True,
            'message': f'Fixed {fixed_count} missing sale records',
            'missing_sales_created': missing_sales,
            'new_totals': {
                'sale_table': new_sale_total,
                'transaction_table': transaction_total,
                'discrepancy': abs(new_sale_total - transaction_total)
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/debug/migrate_transaction_id')
def migrate_transaction_id():
    """Add transaction_id column to Sale table and populate existing data"""
    try:
        from sqlalchemy import text

        # Check if transaction_id column already exists
        result = db.session.execute(text("PRAGMA table_info(sale)")).fetchall()
        columns = [row[1] for row in result]

        migration_steps = []

        if 'transaction_id' not in columns:
            # Add the new column
            db.session.execute(text("ALTER TABLE sale ADD COLUMN transaction_id INTEGER"))
            db.session.commit()
            migration_steps.append("Added transaction_id column to Sale table")
        else:
            migration_steps.append("transaction_id column already exists")

        # Populate existing Sale records with transaction_id
        unlinked_sales = Sale.query.filter(Sale.transaction_id.is_(None)).all()
        linked_count = 0

        for sale in unlinked_sales:
            # Find matching transaction item
            matching_item = TransactionItem.query.filter(
                TransactionItem.product_id == sale.product_id,
                TransactionItem.quantity == sale.quantity,
                TransactionItem.unit_price == sale.unit_price,
                TransactionItem.total_price == sale.total_price
            ).join(Transaction).filter(
                # Match by date proximity (within 1 day)
                db.func.abs(
                    db.func.julianday(Transaction.transaction_date) -
                    db.func.julianday(sale.sale_date)
                ) < 1
            ).first()

            if matching_item:
                sale.transaction_id = matching_item.transaction_id
                linked_count += 1

        # Commit all changes
        db.session.commit()
        migration_steps.append(f"Linked {linked_count} sales to transactions")

        # Verify the migration
        total_sales = Sale.query.count()
        linked_sales = Sale.query.filter(Sale.transaction_id.isnot(None)).count()
        unlinked_sales = total_sales - linked_sales

        return jsonify({
            'success': True,
            'message': 'Migration completed successfully',
            'steps': migration_steps,
            'verification': {
                'total_sales': total_sales,
                'linked_sales': linked_sales,
                'unlinked_sales': unlinked_sales
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/product_stock')
def api_product_stock():
    products = Product.query.order_by(Product.name.asc()).all()
    product_data = []

    for product in products:
        product_data.append({
            'id': product.id,
            'name': product.name,
            'stock': product.stock,
            'price': product.price
        })

    return jsonify({
        'success': True,
        'products': product_data
    })

# Advanced Filtering Routes
@app.route('/sales_filter', methods=['GET', 'POST'])
def sales_filter():
    # Default to last 30 days
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)

    if request.method == 'POST':
        start_date_str = request.form.get('start_date')
        end_date_str = request.form.get('end_date')

        if start_date_str:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        if end_date_str:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()

    # Get comprehensive sales data for the date range with payment method breakdown
    sales_data = db.session.query(
        Product.name,
        db.func.sum(TransactionItem.quantity).label('total_sold'),
        db.func.sum(
            db.case(
                (Transaction.payment_method == 'cash', TransactionItem.total_price),
                else_=0
            )
        ).label('cash_revenue'),
        db.func.sum(
            db.case(
                (Transaction.payment_method == 'credit', TransactionItem.total_price),
                else_=0
            )
        ).label('credit_sales'),
        db.func.sum(TransactionItem.total_price).label('total_revenue')
    ).join(TransactionItem).join(Transaction).filter(
        db.func.date(Transaction.transaction_date) >= start_date,
        db.func.date(Transaction.transaction_date) <= end_date
    ).group_by(Product.id, Product.name).all()

    # Get daily sales breakdown for the period
    daily_sales_raw = db.session.query(
        db.func.date(Transaction.transaction_date).label('sale_date'),
        db.func.sum(
            db.case(
                (Transaction.payment_method == 'cash', Transaction.total_amount),
                else_=0
            )
        ).label('daily_cash_revenue'),
        db.func.sum(
            db.case(
                (Transaction.payment_method == 'credit', Transaction.total_amount),
                else_=0
            )
        ).label('daily_credit_sales'),
        db.func.sum(Transaction.total_amount).label('daily_total'),
        db.func.count(Transaction.id).label('daily_transactions')
    ).filter(
        db.func.date(Transaction.transaction_date) >= start_date,
        db.func.date(Transaction.transaction_date) <= end_date
    ).group_by(db.func.date(Transaction.transaction_date)).order_by(db.func.date(Transaction.transaction_date).desc()).all()

    # Convert string dates to datetime objects for template
    daily_sales = []
    for row in daily_sales_raw:
        if isinstance(row.sale_date, str):
            sale_date = datetime.strptime(row.sale_date, '%Y-%m-%d').date()
        else:
            sale_date = row.sale_date
        daily_sales.append({
            'sale_date': sale_date,
            'daily_cash_revenue': row.daily_cash_revenue or 0,
            'daily_credit_sales': row.daily_credit_sales or 0,
            'daily_total': row.daily_total or 0,
            'daily_transactions': row.daily_transactions or 0
        })

    # Calculate summary totals
    total_cash_revenue = sum(row.cash_revenue or 0 for row in sales_data)
    total_credit_sales = sum(row.credit_sales or 0 for row in sales_data)
    total_items_sold = sum(row.total_sold or 0 for row in sales_data)
    unique_products_sold = len([row for row in sales_data if row.total_sold > 0])

    # Get credit payments received in this period
    credit_payments_received = db.session.query(
        db.func.sum(CreditTransaction.amount)
    ).filter(
        CreditTransaction.transaction_type == 'payment',
        db.func.date(CreditTransaction.created_at) >= start_date,
        db.func.date(CreditTransaction.created_at) <= end_date
    ).scalar() or 0

    return render_template('sales_filter.html',
                         sales_data=sales_data,
                         daily_sales=daily_sales,
                         start_date=start_date,
                         end_date=end_date,
                         total_cash_revenue=total_cash_revenue,
                         total_credit_sales=total_credit_sales,
                         total_items_sold=total_items_sold,
                         unique_products_sold=unique_products_sold,
                         credit_payments_received=credit_payments_received)

@app.route('/inventory_movements')
def inventory_movements():
    # Get products with recent activity, sorted alphabetically
    products = Product.query.order_by(Product.name.asc()).all()

    # Calculate movements with better data
    movements = []
    for product in products:
        # Calculate stock turnover rate
        turnover_rate = (product.sold / product.received * 100) if product.received > 0 else 0

        movements.append({
            'product': product,
            'received_total': product.received,
            'sold_total': product.sold,
            'current_stock': product.stock,
            'stock_value': product.stock * product.price,
            'turnover_rate': turnover_rate,
            'stock_status': 'Low Stock' if product.stock <= 5 else 'In Stock' if product.stock > 0 else 'Out of Stock'
        })

    return render_template('inventory_movements.html', movements=movements)

# Customer Management Routes
@app.route('/customers')
def customers():
    customers = Customer.query.all()
    return render_template('customers.html', customers=customers)

@app.route('/cashier_performance')
def cashier_performance():
    """Cashier performance dashboard with sales, profit, and rankings"""
    try:
        # Get all cashiers
        cashiers = Cashier.query.filter_by(is_active=True).all()

        # Calculate performance metrics for each cashier
        cashier_performance = []

        for cashier in cashiers:
            # Get all transactions for this cashier
            transactions = Transaction.query.filter_by(cashier_id=cashier.id).all()

            # Calculate basic metrics
            total_sales = sum(t.total_amount for t in transactions)
            total_transactions = len(transactions)

            # Calculate total items sold and profit
            total_items_sold = 0
            total_profit = 0

            for transaction in transactions:
                # Get transaction items
                items = TransactionItem.query.filter_by(transaction_id=transaction.id).all()
                for item in items:
                    total_items_sold += item.quantity
                    # Calculate profit using actual selling price (includes reseller discounts)
                    if item.product:
                        profit_per_unit = item.unit_price - item.product.capital_price
                        total_profit += profit_per_unit * item.quantity

            # Calculate averages
            avg_transaction_value = total_sales / total_transactions if total_transactions > 0 else 0
            avg_items_per_transaction = total_items_sold / total_transactions if total_transactions > 0 else 0

            # Get date range
            first_transaction = None
            last_transaction = None
            if transactions:
                first_transaction = min(t.transaction_date for t in transactions)
                last_transaction = max(t.transaction_date for t in transactions)

            cashier_performance.append({
                'cashier': cashier,
                'total_sales': total_sales,
                'total_profit': total_profit,
                'total_transactions': total_transactions,
                'total_items_sold': total_items_sold,
                'avg_transaction_value': avg_transaction_value,
                'avg_items_per_transaction': avg_items_per_transaction,
                'first_transaction': first_transaction,
                'last_transaction': last_transaction,
                'profit_margin': (total_profit / total_sales * 100) if total_sales > 0 else 0
            })

        # Sort by total sales for ranking
        cashier_performance.sort(key=lambda x: x['total_sales'], reverse=True)

        # Add rankings
        for i, performance in enumerate(cashier_performance):
            performance['sales_rank'] = i + 1

        # Sort by total profit for profit ranking
        profit_sorted = sorted(cashier_performance, key=lambda x: x['total_profit'], reverse=True)
        for i, performance in enumerate(profit_sorted):
            performance['profit_rank'] = i + 1

        # Sort by total transactions for activity ranking
        transaction_sorted = sorted(cashier_performance, key=lambda x: x['total_transactions'], reverse=True)
        for i, performance in enumerate(transaction_sorted):
            performance['activity_rank'] = i + 1

        # Calculate overall totals
        total_company_sales = sum(p['total_sales'] for p in cashier_performance)
        total_company_profit = sum(p['total_profit'] for p in cashier_performance)
        total_company_transactions = sum(p['total_transactions'] for p in cashier_performance)
        total_company_items = sum(p['total_items_sold'] for p in cashier_performance)

        return render_template('cashier_performance.html',
                             cashier_performance=cashier_performance,
                             total_company_sales=total_company_sales,
                             total_company_profit=total_company_profit,
                             total_company_transactions=total_company_transactions,
                             total_company_items=total_company_items)

    except Exception as e:
        flash(f'Error loading cashier performance: {str(e)}', 'danger')
        return redirect(url_for('index'))

@app.route('/transaction_editor')
def transaction_editor():
    """Transaction date editor page"""
    return render_template('transaction_editor.html')

@app.route('/api/transactions')
def api_transactions():
    """API endpoint to get all transactions for editing"""
    try:
        transactions = Transaction.query.order_by(Transaction.transaction_date.desc()).all()
        transaction_list = []

        for transaction in transactions:
            # Get transaction items
            items = TransactionItem.query.filter_by(transaction_id=transaction.id).all()
            item_details = []

            for item in items:
                product = Product.query.get(item.product_id)
                item_details.append({
                    'product_name': product.name if product else 'Unknown Product',
                    'quantity': item.quantity,
                    'unit_price': item.unit_price,
                    'total_price': item.total_price
                })

            transaction_data = {
                'id': transaction.id,
                'transaction_number': transaction.transaction_number,
                'total_amount': transaction.total_amount,
                'cashier_name': transaction.cashier.name if transaction.cashier else 'No Cashier',
                'transaction_date': transaction.transaction_date.strftime('%Y-%m-%d %H:%M:%S'),
                'transaction_date_iso': transaction.transaction_date.isoformat(),
                'items': item_details,
                'item_count': len(item_details)
            }
            transaction_list.append(transaction_data)

        return jsonify({
            'success': True,
            'transactions': transaction_list
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/transactions/<int:transaction_id>/update_date', methods=['POST'])
def update_transaction_date(transaction_id):
    """API endpoint to update transaction date"""
    try:
        data = request.get_json()
        new_date_str = data.get('new_date')

        if not new_date_str:
            return jsonify({
                'success': False,
                'error': 'New date is required'
            }), 400

        # Parse the new date
        try:
            # Handle both datetime and date formats
            if 'T' in new_date_str:
                new_date = datetime.fromisoformat(new_date_str.replace('Z', '+00:00'))
            else:
                # If only date is provided, add current time
                date_part = datetime.strptime(new_date_str, '%Y-%m-%d').date()
                current_time = datetime.now().time()
                new_date = datetime.combine(date_part, current_time)
                new_date = new_date.replace(tzinfo=timezone.utc)
        except ValueError as e:
            return jsonify({
                'success': False,
                'error': f'Invalid date format: {str(e)}'
            }), 400

        # Get the transaction
        transaction = Transaction.query.get(transaction_id)
        if not transaction:
            return jsonify({
                'success': False,
                'error': 'Transaction not found'
            }), 404

        old_date = transaction.transaction_date

        # Update transaction date
        transaction.transaction_date = new_date

        # Update related sales records
        sales = Sale.query.filter_by(transaction_id=transaction_id).all()
        for sale in sales:
            sale.sale_date = new_date

        db.session.commit()

        # Trigger CSV backup
        backup_all_tables_on_change()

        return jsonify({
            'success': True,
            'message': f'Transaction date updated successfully',
            'old_date': old_date.strftime('%Y-%m-%d'),
            'new_date': new_date.strftime('%Y-%m-%d'),
            'transaction_number': transaction.transaction_number
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/add_customer', methods=['GET', 'POST'])
def add_customer():
    if request.method == 'POST':
        name = request.form['name']
        phone = request.form.get('phone', '')
        address = request.form.get('address', '')

        customer = Customer(name=name, phone=phone, address=address)
        db.session.add(customer)
        db.session.commit()

        # Backup to CSV after customer addition
        backup_all_tables_on_change()

        flash('Customer added successfully!', 'success')
        return redirect(url_for('customers'))

    return render_template('add_customer.html')

@app.route('/edit_customer/<int:id>', methods=['GET', 'POST'])
def edit_customer(id):
    customer = Customer.query.get_or_404(id)

    if request.method == 'POST':
        customer.name = request.form['name']
        customer.phone = request.form.get('phone', '')
        customer.address = request.form.get('address', '')
        customer.updated_at = datetime.now(timezone.utc)

        db.session.commit()
        flash('Customer updated successfully!', 'success')
        return redirect(url_for('customers'))

    return render_template('edit_customer.html', customer=customer)

# Credit Management Routes
@app.route('/credit_management')
def credit_management():
    customers_with_credit = Customer.query.filter(Customer.total_credit > Customer.total_paid).all()
    recent_credits = CreditTransaction.query.order_by(CreditTransaction.created_at.desc()).limit(20).all()
    return render_template('credit_management.html',
                         customers_with_credit=customers_with_credit,
                         recent_credits=recent_credits)

@app.route('/customer_credit/<int:customer_id>')
def customer_credit(customer_id):
    customer = Customer.query.get_or_404(customer_id)
    credit_history = CreditTransaction.query.filter_by(customer_id=customer_id).order_by(CreditTransaction.created_at.desc()).all()
    return render_template('customer_credit.html', customer=customer, credit_history=credit_history)

@app.route('/add_credit/<int:customer_id>', methods=['POST'])
def add_credit(customer_id):
    customer = Customer.query.get_or_404(customer_id)
    amount = float(request.form['amount'])
    description = request.form.get('description', '')

    # Create credit transaction
    credit_transaction = CreditTransaction(
        customer_id=customer_id,
        amount=amount,
        transaction_type='credit',
        description=description
    )
    db.session.add(credit_transaction)

    # Update customer total credit
    customer.total_credit += amount
    customer.updated_at = datetime.now(timezone.utc)

    db.session.commit()
    flash(f'Credit of ₱{amount:,.2f} added for {customer.name}', 'success')
    return redirect(url_for('customer_credit', customer_id=customer_id))

@app.route('/add_payment/<int:customer_id>', methods=['POST'])
def add_payment(customer_id):
    customer = Customer.query.get_or_404(customer_id)
    amount = float(request.form['amount'])
    description = request.form.get('description', 'Payment received')

    # Create payment transaction
    payment_transaction = CreditTransaction(
        customer_id=customer_id,
        amount=amount,
        transaction_type='payment',
        description=description,
        is_paid=True
    )
    db.session.add(payment_transaction)

    # Update customer total paid
    customer.total_paid += amount
    customer.updated_at = datetime.now(timezone.utc)

    db.session.commit()
    flash(f'Payment of ₱{amount:,.2f} recorded for {customer.name}', 'success')
    return redirect(url_for('customer_credit', customer_id=customer_id))

if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # Initialize with sample data if database is empty
        if Product.query.count() == 0:
            from init_data import init_sample_data
            init_sample_data()

    # Get port from environment variable or default to 5000
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV') != 'production'

    app.run(host='0.0.0.0', port=port, debug=debug)
