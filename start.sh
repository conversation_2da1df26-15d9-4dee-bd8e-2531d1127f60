#!/bin/bash

echo "🏪 ATE MEG's FROZEN FOODS POS System Launcher"
echo "==============================================="

# Check if Python is available
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "❌ Python is not installed or not in PATH"
    echo "💡 Please install Python from https://python.org"
    echo "💡 On Ubuntu/Debian: sudo apt install python3"
    echo "💡 On macOS: brew install python3"
    exit 1
fi

# Try python3 first, then python
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
fi

echo "✅ Python found: $PYTHON_CMD"
echo "🔄 Starting application..."
echo

# Start the application
$PYTHON_CMD start_app.py

echo
echo "🛑 Application stopped"
