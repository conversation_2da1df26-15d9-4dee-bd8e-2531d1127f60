#!/usr/bin/env python3
"""
Add cashier system to the database by recreating it with the new schema
"""

import os
import shutil
from app import app, db, Cashier

def add_cashier_system():
    """Add cashier table by recreating database with new schema"""
    with app.app_context():
        print("🔧 Adding cashier system to database...")

        # Backup existing data using raw SQL
        print("📦 Backing up existing data...")

        # Use raw SQL to get data without model conflicts
        result = db.session.execute(db.text("SELECT * FROM product"))
        product_data = [dict(row._mapping) for row in result]

        result = db.session.execute(db.text("SELECT * FROM 'transaction'"))
        transaction_data = [dict(row._mapping) for row in result]

        result = db.session.execute(db.text("SELECT * FROM customer"))
        customer_data = [dict(row._mapping) for row in result]

        result = db.session.execute(db.text("SELECT * FROM sale"))
        sale_data = [dict(row._mapping) for row in result]

        result = db.session.execute(db.text("SELECT * FROM transaction_item"))
        transaction_item_data = [dict(row._mapping) for row in result]

        try:
            result = db.session.execute(db.text("SELECT * FROM credit_transaction"))
            credit_transaction_data = [dict(row._mapping) for row in result]
        except:
            credit_transaction_data = []

        print(f"📊 Backed up {len(product_data)} products, {len(transaction_data)} transactions, {len(customer_data)} customers")

        # Drop and recreate all tables
        print("🗑️ Dropping existing tables...")
        db.drop_all()

        print("🏗️ Creating new tables with cashier support...")
        db.create_all()

        # Create default cashier
        default_cashier = Cashier(name="Default Cashier")
        db.session.add(default_cashier)
        db.session.commit()
        print("✅ Default cashier created")

        # Restore data using raw SQL
        print("📦 Restoring products...")
        for p_data in product_data:
            db.session.execute(db.text("""
                INSERT INTO product (id, name, price, capital_ratio, stock, received, sold, created_at, updated_at)
                VALUES (:id, :name, :price, :capital_ratio, :stock, :received, :sold, :created_at, :updated_at)
            """), p_data)

        print("👥 Restoring customers...")
        for c_data in customer_data:
            db.session.execute(db.text("""
                INSERT INTO customer (id, name, phone, address, total_credit, total_paid, created_at, updated_at)
                VALUES (:id, :name, :phone, :address, :total_credit, :total_paid, :created_at, :updated_at)
            """), c_data)

        print("💰 Restoring transactions...")
        for t_data in transaction_data:
            db.session.execute(db.text("""
                INSERT INTO 'transaction' (id, transaction_number, total_amount, cashier_id, transaction_date)
                VALUES (:id, :transaction_number, :total_amount, :cashier_id, :transaction_date)
            """), {
                'id': t_data['id'],
                'transaction_number': t_data['transaction_number'],
                'total_amount': t_data['total_amount'],
                'cashier_id': default_cashier.id,
                'transaction_date': t_data['transaction_date']
            })

        print("📋 Restoring sales...")
        for s_data in sale_data:
            db.session.execute(db.text("""
                INSERT INTO sale (id, product_id, transaction_id, quantity, unit_price, total_price, sale_date)
                VALUES (:id, :product_id, :transaction_id, :quantity, :unit_price, :total_price, :sale_date)
            """), s_data)

        print("🧾 Restoring transaction items...")
        for ti_data in transaction_item_data:
            db.session.execute(db.text("""
                INSERT INTO transaction_item (id, transaction_id, product_id, quantity, unit_price, total_price)
                VALUES (:id, :transaction_id, :product_id, :quantity, :unit_price, :total_price)
            """), ti_data)

        if credit_transaction_data:
            print("💳 Restoring credit transactions...")
            for ct_data in credit_transaction_data:
                db.session.execute(db.text("""
                    INSERT INTO credit_transaction (id, customer_id, transaction_id, amount, transaction_type, description, is_paid, created_at)
                    VALUES (:id, :customer_id, :transaction_id, :amount, :transaction_type, :description, :is_paid, :created_at)
                """), ct_data)

        db.session.commit()

        print("🎉 Cashier system setup complete!")
        print("💡 All existing transactions have been assigned to 'Default Cashier'")
        print("💡 You can now add more cashiers and edit transaction cashiers as needed")

if __name__ == "__main__":
    add_cashier_system()
