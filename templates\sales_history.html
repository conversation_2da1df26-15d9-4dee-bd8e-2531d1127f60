{% extends "base.html" %} {% block title %}Sales History - ATE MEG's FROZEN
FOODS{% endblock %} {% block content %}

<!-- Sales History Header -->
<div class="row mb-4">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <h1 class="display-6 fw-bold text-primary mb-2">
          <i class="fas fa-receipt me-3"></i>Sales History Management
        </h1>
        <p class="text-muted fs-5">
          Add new transactions and view sales history
        </p>
      </div>
      <div>
        <a href="{{ url_for('reports') }}" class="btn btn-outline-primary">
          <i class="fas fa-arrow-left me-2"></i>Back to Reports
        </a>
      </div>
    </div>
  </div>
</div>

<!-- New Transaction Form -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
          <i class="fas fa-plus-circle me-2"></i>Add New Transaction
        </h5>
      </div>
      <div class="card-body">
        <form id="transactionForm">
          <div class="row mb-3">
            <div class="col-md-2">
              <label for="cashier_id" class="form-label">Cashier</label>
              <select
                class="form-select"
                id="cashier_id"
                name="cashier_id"
                required
              >
                <option value="">Select Cashier</option>
                {% for cashier in cashiers %}
                <option value="{{ cashier.id }}">{{ cashier.name }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="col-md-2">
              <label for="customer_type" class="form-label"
                >Customer Type</label
              >
              <select
                class="form-select"
                id="customer_type"
                name="customer_type"
                required
              >
                <option value="default">Default</option>
                <option value="reseller">
                  Reseller (-₱<span id="reseller-discount">5</span>)
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <label for="payment_method" class="form-label"
                >Payment Method</label
              >
              <select
                class="form-select"
                id="payment_method"
                name="payment_method"
                required
              >
                <option value="cash">Cash</option>
                <option value="credit">Credit</option>
              </select>
            </div>
            <div class="col-md-3" id="customer_section" style="display: none">
              <label for="customer_id" class="form-label"
                >Customer (for Credit)</label
              >
              <select class="form-select" id="customer_id" name="customer_id">
                <option value="">Select Customer</option>
                {% for customer in customers %}
                <option value="{{ customer.id }}">{{ customer.name }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="col-md-3">
              <label for="transaction_date" class="form-label"
                >Transaction Date</label
              >
              <input
                type="date"
                class="form-control"
                id="transaction_date"
                name="transaction_date"
                required
              />
            </div>
          </div>

          <!-- Items Section -->
          <div class="mb-3">
            <label class="form-label">Transaction Items</label>
            <div id="items-container">
              <div class="item-row row mb-2">
                <div class="col-md-6">
                  <select
                    class="form-select product-select"
                    name="product_id[]"
                    required
                  >
                    <option value="">Select Product</option>
                    {% for product in products %}
                    <option
                      value="{{ product.id }}"
                      data-price="{{ product.price }}"
                      data-stock="{{ product.stock }}"
                    >
                      {{ product.name }} - ₱{{ "%.2f"|format(product.price) }}
                      (Stock: {{ product.stock }})
                    </option>
                    {% endfor %}
                  </select>
                </div>
                <div class="col-md-2">
                  <input
                    type="number"
                    class="form-control quantity-input"
                    name="quantity[]"
                    placeholder="Qty"
                    min="1"
                    required
                  />
                </div>
                <div class="col-md-2">
                  <input
                    type="text"
                    class="form-control unit-price"
                    readonly
                    placeholder="Unit Price"
                  />
                </div>
                <div class="col-md-2">
                  <input
                    type="text"
                    class="form-control total-price"
                    readonly
                    placeholder="Total"
                  />
                </div>
              </div>
            </div>
            <button
              type="button"
              class="btn btn-outline-primary btn-sm"
              id="add-item"
            >
              <i class="fas fa-plus"></i> Add Item
            </button>
          </div>

          <!-- Transaction Summary -->
          <div class="row mb-3">
            <div class="col-md-6">
              <div class="card bg-light">
                <div class="card-body">
                  <h6>Transaction Summary</h6>
                  <div class="d-flex justify-content-between">
                    <span>Total Items:</span>
                    <span id="total-items">0</span>
                  </div>
                  <div class="d-flex justify-content-between">
                    <span><strong>Total Amount:</strong></span>
                    <span id="total-amount"><strong>₱0.00</strong></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="d-flex gap-2">
            <button type="submit" class="btn btn-success">
              <i class="fas fa-save me-2"></i>Create Transaction
            </button>
            <button type="button" class="btn btn-secondary" id="reset-form">
              <i class="fas fa-undo me-2"></i>Reset Form
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Recent Transactions -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-history me-2"></i>Recent Transactions (Last 50)
        </h5>
      </div>
      <div class="card-body">
        {% if transactions %}
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead class="table-dark">
              <tr>
                <th>Transaction #</th>
                <th>Date & Time</th>
                <th>Cashier</th>
                <th>Payment</th>
                <th>Items</th>
                <th>Total Amount</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for transaction in transactions %}
              <tr>
                <td><strong>{{ transaction.transaction_number }}</strong></td>
                <td>
                  {{ transaction.transaction_date.strftime('%Y-%m-%d %H:%M:%S')
                  }}
                </td>
                <td>
                  <span class="badge bg-primary">
                    {{ transaction.cashier.name if transaction.cashier else 'No
                    Cashier' }}
                  </span>
                </td>
                <td>
                  <span
                    class="badge {{ 'bg-success' if transaction.payment_method == 'cash' else 'bg-warning' }}"
                  >
                    {{ transaction.payment_method.title() }}
                  </span>
                </td>
                <td>
                  <span class="badge bg-info"
                    >{{ transaction.items|length }} item(s)</span
                  >
                </td>
                <td class="text-success">
                  <strong
                    >₱{{ "{:,.2f}".format(transaction.total_amount) }}</strong
                  >
                </td>
                <td>
                  <button
                    class="btn btn-sm btn-outline-primary"
                    onclick="viewTransactionDetails('{{ transaction.id }}')"
                    data-bs-toggle="modal"
                    data-bs-target="#transactionModal"
                  >
                    <i class="fas fa-eye"></i> View
                  </button>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        {% else %}
        <div class="text-center py-4">
          <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
          <p class="text-muted">
            No transactions found. Create your first transaction above!
          </p>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- Transaction Details Modal -->
<div class="modal fade" id="transactionModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Transaction Details</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body" id="transactionDetails">
        <!-- Transaction details will be loaded here -->
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Set current date as default (date only)
    const now = new Date();
    const today = now.toISOString().slice(0, 10);
    document.getElementById("transaction_date").value = today;

    // Load reseller discount amount
    loadResellerDiscount();

    // Handle customer type change to update calculations
    document
      .getElementById("customer_type")
      .addEventListener("change", function () {
        // Update all row calculations when customer type changes
        document.querySelectorAll(".item-row").forEach((row) => {
          updateRowCalculation(row);
        });
        updateCalculations();
      });

    // Handle payment method change
    document
      .getElementById("payment_method")
      .addEventListener("change", function () {
        const customerSection = document.getElementById("customer_section");
        if (this.value === "credit") {
          customerSection.style.display = "block";
          document.getElementById("customer_id").required = true;
        } else {
          customerSection.style.display = "none";
          document.getElementById("customer_id").required = false;
        }
      });

    // Add item functionality
    document.getElementById("add-item").addEventListener("click", function () {
      const container = document.getElementById("items-container");
      const newRow = container.querySelector(".item-row").cloneNode(true);

      // Clear values in new row
      newRow.querySelectorAll("input, select").forEach((input) => {
        input.value = "";
      });

      container.appendChild(newRow);
      updateCalculations();
    });

    // Handle product selection and calculations
    document.addEventListener("change", function (e) {
      if (
        e.target.classList.contains("product-select") ||
        e.target.classList.contains("quantity-input")
      ) {
        updateRowCalculation(e.target.closest(".item-row"));
        updateCalculations();
      }
    });

    function updateRowCalculation(row) {
      const productSelect = row.querySelector(".product-select");
      const quantityInput = row.querySelector(".quantity-input");
      const unitPriceInput = row.querySelector(".unit-price");
      const totalPriceInput = row.querySelector(".total-price");

      if (productSelect.value && quantityInput.value) {
        const selectedOption = productSelect.selectedOptions[0];
        let price = parseFloat(selectedOption.dataset.price);
        const quantity = parseInt(quantityInput.value);
        const stock = parseInt(selectedOption.dataset.stock);

        if (quantity > stock) {
          alert(`Insufficient stock! Available: ${stock}`);
          quantityInput.value = stock;
          return;
        }

        // Apply reseller discount if selected
        const customerType = document.getElementById("customer_type").value;
        if (customerType === "reseller") {
          const discount =
            parseFloat(
              document.getElementById("reseller-discount").textContent
            ) || 5;
          price = Math.max(0, price - discount); // Ensure price doesn't go negative
        }

        unitPriceInput.value = `₱${price.toFixed(2)}`;
        totalPriceInput.value = `₱${(price * quantity).toFixed(2)}`;
      } else {
        unitPriceInput.value = "";
        totalPriceInput.value = "";
      }
    }

    function updateCalculations() {
      let totalItems = 0;
      let totalAmount = 0;
      const customerType = document.getElementById("customer_type").value;
      const discount =
        customerType === "reseller"
          ? parseFloat(
              document.getElementById("reseller-discount").textContent
            ) || 5
          : 0;

      document.querySelectorAll(".item-row").forEach((row) => {
        const quantityInput = row.querySelector(".quantity-input");
        const productSelect = row.querySelector(".product-select");

        if (productSelect.value && quantityInput.value) {
          const quantity = parseInt(quantityInput.value);
          let price = parseFloat(
            productSelect.selectedOptions[0].dataset.price
          );

          // Apply reseller discount
          if (customerType === "reseller") {
            price = Math.max(0, price - discount);
          }

          totalItems += quantity;
          totalAmount += price * quantity;
        }
      });

      document.getElementById("total-items").textContent = totalItems;
      document.getElementById(
        "total-amount"
      ).innerHTML = `<strong>₱${totalAmount.toFixed(2)}</strong>`;
    }

    // Form submission
    document
      .getElementById("transactionForm")
      .addEventListener("submit", function (e) {
        e.preventDefault();

        const formData = new FormData(this);
        const items = [];

        // Collect items data
        const productIds = formData.getAll("product_id[]");
        const quantities = formData.getAll("quantity[]");

        for (let i = 0; i < productIds.length; i++) {
          if (productIds[i] && quantities[i]) {
            items.push({
              product_id: productIds[i],
              quantity: quantities[i],
            });
          }
        }

        if (items.length === 0) {
          alert("Please add at least one item to the transaction.");
          return;
        }

        const transactionData = {
          cashier_id: formData.get("cashier_id"),
          payment_method: formData.get("payment_method"),
          customer_id: formData.get("customer_id"),
          customer_type: formData.get("customer_type"),
          transaction_date: formData.get("transaction_date"),
          items: items,
        };

        // Submit transaction
        fetch("/api/create_transaction", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(transactionData),
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              alert(
                `Transaction ${
                  data.transaction_number
                } created successfully! Total: ₱${data.total_amount.toFixed(2)}`
              );
              location.reload(); // Refresh to show new transaction
            } else {
              alert(`Error: ${data.message}`);
            }
          })
          .catch((error) => {
            console.error("Error:", error);
            alert("An error occurred while creating the transaction.");
          });
      });

    // Reset form
    document
      .getElementById("reset-form")
      .addEventListener("click", function () {
        document.getElementById("transactionForm").reset();

        // Keep only one item row
        const container = document.getElementById("items-container");
        const firstRow = container.querySelector(".item-row");
        container.innerHTML = "";
        container.appendChild(firstRow);

        // Reset calculations
        updateCalculations();

        // Reset date to current (date only)
        const now = new Date();
        const today = now.toISOString().slice(0, 10);
        document.getElementById("transaction_date").value = today;
      });
  });

  // Load reseller discount amount from server
  function loadResellerDiscount() {
    fetch("/api/get_reseller_discount")
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          document.getElementById("reseller-discount").textContent =
            data.discount;
        }
      })
      .catch((error) => {
        console.error("Error loading reseller discount:", error);
      });
  }

  // Transaction details modal function
  function viewTransactionDetails(transactionId) {
    // This function will be implemented to show transaction details
    document.getElementById("transactionDetails").innerHTML =
      "<p>Loading transaction details...</p>";

    // You can implement AJAX call here to fetch transaction details
    // For now, we'll show a placeholder
    setTimeout(() => {
      document.getElementById("transactionDetails").innerHTML = `
            <p>Transaction details for ID: ${transactionId}</p>
            <p>This feature can be enhanced to show full transaction breakdown.</p>
        `;
    }, 500);
  }
</script>

{% endblock %}
