#!/usr/bin/env python3
"""
Update product prices for ATE MEG's FROZEN FOODS
"""

from app import app, db, Product

def update_product_prices():
    """Update all product prices according to the new price list"""
    
    # New price list
    price_updates = {
        "Beef Burger Patty": 80,
        "Big Jumbo Siomai": 70,  # Updated name mapping
        "Cheesy Hambutid": 90,   # Updated name mapping  
        "Chicken Nuggets": 60,
        "Chicken Pastil": 110,
        "Chicken Teriyaki": 80,
        "Chicken Tocino": 80,
        "Pork Dinakdakan": 110,
        "Pork Longadog": 65,
        "Pork Longanisa": 65,
        "Pork Meatballs": 60,
        "Pork Tapa": 75,
        "Pork Teriyaki": 75,
        "Pork Tocino": 75,
        "Pork Sisig": 110,
        "Salami w/ Cheese": 90,
        "Skinless Longanisa": 65,
        "Sliced Ham": 75,
    }
    
    # Name mappings for products that might have slightly different names
    name_mappings = {
        "Big Siomai": "Big Jumbo Siomai",
        "Cheesy Hamonado": "Cheesy Hambutid",
    }
    
    with app.app_context():
        print("🔄 Updating product prices...")
        
        updated_count = 0
        
        for product_name, new_price in price_updates.items():
            # Try to find the product by exact name first
            product = Product.query.filter_by(name=product_name).first()
            
            # If not found, try alternative names
            if not product:
                for alt_name, mapped_name in name_mappings.items():
                    if mapped_name == product_name:
                        product = Product.query.filter_by(name=alt_name).first()
                        break
            
            if product:
                old_price = product.price
                product.price = new_price
                print(f"✅ {product.name}: ₱{old_price:.2f} → ₱{new_price:.2f}")
                updated_count += 1
            else:
                print(f"❌ Product not found: {product_name}")
        
        # Commit all changes
        db.session.commit()
        
        print(f"\n🎉 Successfully updated {updated_count} product prices!")
        
        # Display all current prices
        print("\n📋 Current Price List:")
        print("-" * 40)
        products = Product.query.order_by(Product.name).all()
        for product in products:
            print(f"{product.name:<25} ₱{product.price:.2f}")

if __name__ == "__main__":
    update_product_prices()
