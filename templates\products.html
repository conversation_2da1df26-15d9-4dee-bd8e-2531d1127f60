{% extends "base.html" %} {% block title %}Product Management - ATE MEG's FROZEN
FOODS{% endblock %} {% block extra_css %}
<style>
  /* Price forms removed - prices are read-only in main view */
  .profit-highlight {
    background-color: #d4edda;
    border-radius: 4px;
    padding: 2px 6px;
  }

  /* Mobile responsiveness improvements */
  @media (max-width: 768px) {
    h1 {
      font-size: 1.5rem !important;
      line-height: 1.2;
    }

    .table-responsive {
      font-size: 0.85rem;
    }

    .btn {
      font-size: 0.875rem;
    }

    /* Ensure header is always visible */
    .card-header h5 {
      font-size: 1.1rem;
      margin-bottom: 0;
    }
  }

  @media (max-width: 576px) {
    h1 {
      font-size: 1.25rem !important;
    }

    .table-responsive {
      font-size: 0.8rem;
    }
  }
</style>
{% endblock %} {% block content %}
<div class="row mb-4">
  <div class="col-12">
    <!-- Mobile-friendly header -->
    <div
      class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center"
    >
      <h1 class="mb-2 mb-md-0">
        <i class="fas fa-box"></i> Products Management
      </h1>
      <a href="{{ url_for('add_product') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i>
        <span class="d-none d-sm-inline">Add New Product</span>
        <span class="d-inline d-sm-none">Add</span>
      </a>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">Product Inventory</h5>
      </div>
      <div class="card-body">
        {% if products %}
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead class="table-dark">
              <tr>
                <th>Product Name</th>
                <th>Selling Price</th>
                <th>Capital Price</th>
                <th>Profit/Unit</th>
                <th>Received</th>
                <th>Sold</th>
                <th>Stock</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for product in products %}
              <tr
                class="{% if product.stock == 0 %}out-of-stock{% elif product.stock <= 5 %}low-stock{% endif %}"
              >
                <td>
                  <strong>{{ product.name }}</strong>
                </td>
                <td>
                  <span class="text-primary">
                    <strong>₱{{ "{:,.2f}".format(product.price) }}</strong>
                  </span>
                </td>
                <td>
                  <span class="text-info">
                    ₱{{ "{:,.2f}".format(product.capital_price) }}
                  </span>
                </td>
                <td class="text-success">
                  <span class="profit-highlight">
                    <strong
                      >₱{{ "{:,.2f}".format(product.profit_per_unit) }}</strong
                    >
                  </span>
                </td>
                <td>{{ product.received }}</td>
                <td>{{ product.sold }}</td>
                <td>
                  <span
                    class="badge {% if product.stock == 0 %}bg-danger{% elif product.stock <= 5 %}bg-warning{% else %}bg-success{% endif %}"
                  >
                    {{ product.stock }}
                  </span>
                </td>
                <td>
                  {% if product.stock == 0 %}
                  <span class="badge bg-danger">Out of Stock</span>
                  {% elif product.stock <= 5 %}
                  <span class="badge bg-warning">Low Stock</span>
                  {% else %}
                  <span class="badge bg-success">In Stock</span>
                  {% endif %}
                </td>
                <td>
                  <div class="btn-group" role="group">
                    <a
                      href="{{ url_for('edit_product', id=product.id) }}"
                      class="btn btn-sm btn-outline-primary"
                      title="Edit Product"
                    >
                      <i class="fas fa-edit"></i>
                    </a>
                    <a
                      href="{{ url_for('product_history', id=product.id) }}"
                      class="btn btn-sm btn-outline-info"
                      title="View History"
                    >
                      <i class="fas fa-history"></i>
                    </a>
                    <a
                      href="{{ url_for('delete_product', id=product.id) }}"
                      class="btn btn-sm btn-outline-danger"
                      onclick="return confirmDelete('{{ product.name }}', {{ product.stock }}, {{ product.sold }})"
                      title="Delete Product"
                    >
                      <i class="fas fa-trash"></i>
                    </a>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        {% else %}
        <div class="text-center py-5">
          <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
          <h4 class="text-muted">No products found</h4>
          <p class="text-muted">
            Start by adding your first product to the inventory.
          </p>
          <a href="{{ url_for('add_product') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add First Product
          </a>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

{% if products %}
<div class="row mt-4">
  <div class="col-md-4">
    <div class="card">
      <div class="card-body text-center">
        <h5 class="card-title text-primary">Total Products</h5>
        <h2 class="text-primary">{{ products|length }}</h2>
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="card">
      <div class="card-body text-center">
        <h5 class="card-title text-success">Total Stock Value</h5>
        <h2 class="text-success">
          ₱{{ "{:,.2f}".format(products|sum(attribute='stock')|float *
          products|map(attribute='price')|sum / products|length if products else
          0) }}
        </h2>
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="card">
      <div class="card-body text-center">
        <h5 class="card-title text-warning">Low Stock Items</h5>
        <h2 class="text-warning">
          {{ products|selectattr('stock', '<=', 5)|list|length }}
        </h2>
      </div>
    </div>
  </div>
</div>
{% endif %} {% endblock %} {% block scripts %}
<script>
  function confirmDelete(productName, stock, sold) {
    let message = `Are you sure you want to delete "${productName}"?\n\n`;
    message += `This product has:\n`;
    message += `• ${stock} units in stock\n`;
    message += `• ${sold} units sold\n\n`;
    message += `This action cannot be undone!`;

    return confirm(message);
  }
</script>
{% endblock %}
