#!/usr/bin/env python3
"""
Simple run script for the Frozen Foods POS System
"""

import os
import subprocess
import sys

def setup_and_run():
    """Setup database and run the application"""

    # Check if database exists
    if not os.path.exists('frozen_foods_pos.db'):
        print("Database not found. Initializing with sample data...")
        subprocess.run([sys.executable, 'init_data.py'])

    print("Starting Frozen Foods POS System...")
    print("Open your browser and go to: http://localhost:5000")
    print("Press Ctrl+C to stop the server")

    # Run the Flask application
    subprocess.run([sys.executable, 'app.py'])

if __name__ == "__main__":
    setup_and_run()
