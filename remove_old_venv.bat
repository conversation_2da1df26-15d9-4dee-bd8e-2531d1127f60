@echo off
echo 🗑️  Removing Old Virtual Environment
echo =====================================

if exist venv (
    echo 🔍 Found existing venv folder...
    echo ⚠️  This will remove the old virtual environment
    echo 💡 You can recreate it by running setup_venv.py
    echo.
    set /p confirm="Continue? (y/n): "
    
    if /i "%confirm%"=="y" (
        echo 🗑️  Removing venv folder...
        rmdir /s /q venv
        if exist venv (
            echo ❌ Could not remove venv folder completely
            echo 💡 You may need to delete it manually
        ) else (
            echo ✅ Old virtual environment removed successfully
            echo 💡 Run setup_venv.py to create a new one for this device
        )
    ) else (
        echo ❌ Operation cancelled
    )
) else (
    echo ✅ No venv folder found - nothing to remove
)

echo.
pause
