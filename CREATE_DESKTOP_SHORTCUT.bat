@echo off
echo 🔗 Creating Desktop Shortcut for ATE MEG's POS System...

REM Get current directory
set CURRENT_DIR=%~dp0

REM Create VBS script to create shortcut
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%USERPROFILE%\Desktop\ATE MEG POS System.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%CURRENT_DIR%RUN_POS_SYSTEM.bat" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%CURRENT_DIR%" >> CreateShortcut.vbs
echo oLink.Description = "ATE MEG's Frozen Foods POS System" >> CreateShortcut.vbs
echo oLink.IconLocation = "%CURRENT_DIR%static\favicon.ico" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs

REM Run the VBS script
cscript CreateShortcut.vbs >nul

REM Clean up
del CreateShortcut.vbs

echo ✅ Desktop shortcut created successfully!
echo 🖱️  You can now double-click "ATE MEG POS System" on your desktop to start the system
echo.
pause
