{"name": "FROZEN_FOODS_POS", "image": "mcr.microsoft.com/devcontainers/python:3.10", "customizations": {"vscode": {"settings": {"python.defaultInterpreterPath": "/usr/local/bin/python", "python.terminal.activateEnvironment": true}, "extensions": ["ms-python.python"]}}, "forwardPorts": [5000], "portsAttributes": {"5000": {"label": "POS System", "onAutoForward": "openBrowser"}}, "postCreateCommand": "pip install -r requirements.txt && python init_data.py && echo '🎉 Setup complete! Run: python start.py'"}