{% extends "base.html" %} {% block title %}Sales Analytics - ATE MEG's FROZEN
FOODS{% endblock %} {% block content %}
<!-- Modern Sales Report Header -->
<div class="row mb-4">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <h1 class="display-6 fw-bold text-primary mb-2">
          <i class="fas fa-chart-line me-3"></i>Sales Analytics
        </h1>
        <p class="text-muted fs-5">Comprehensive sales performance analysis</p>
      </div>
      <div>
        <a href="{{ url_for('reports') }}" class="btn btn-outline-primary me-2">
          <i class="fas fa-arrow-left me-2"></i>Back to Reports
        </a>
        <a href="{{ url_for('sales_filter') }}" class="btn btn-primary">
          <i class="fas fa-filter me-2"></i>Filter Sales
        </a>
      </div>
    </div>
  </div>
</div>

<!-- Sales Performance Metrics -->
<div class="row mb-5">
  <div class="col-md-3 mb-3">
    <div class="card card-gradient-primary text-white h-100">
      <div class="card-body text-center">
        <div class="mb-3">
          <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
        </div>
        <h6 class="text-uppercase fw-bold opacity-75 mb-2">Total Revenue</h6>
        <h3 class="display-6 fw-bold mb-0">
          ₱{{ "{:,.2f}".format(sales_data|sum(attribute='total_revenue') or 0)
          }}
        </h3>
      </div>
    </div>
  </div>
  <div class="col-md-3 mb-3">
    <div class="card card-gradient-success text-white h-100">
      <div class="card-body text-center">
        <div class="mb-3">
          <i class="fas fa-shopping-cart fa-2x opacity-75"></i>
        </div>
        <h6 class="text-uppercase fw-bold opacity-75 mb-2">Items Sold</h6>
        <h3 class="display-6 fw-bold mb-0">
          {{ sales_data|sum(attribute='total_sold') or 0 }}
        </h3>
      </div>
    </div>
  </div>
  <div class="col-md-3 mb-3">
    <div class="card card-gradient-info text-white h-100">
      <div class="card-body text-center">
        <div class="mb-3">
          <i class="fas fa-box fa-2x opacity-75"></i>
        </div>
        <h6 class="text-uppercase fw-bold opacity-75 mb-2">Products Sold</h6>
        <h3 class="display-6 fw-bold mb-0">{{ sales_data|length }}</h3>
      </div>
    </div>
  </div>
  <div class="col-md-3 mb-3">
    <div class="card card-gradient-warning text-white h-100">
      <div class="card-body text-center">
        <div class="mb-3">
          <i class="fas fa-calculator fa-2x opacity-75"></i>
        </div>
        <h6 class="text-uppercase fw-bold opacity-75 mb-2">Avg. Sale Value</h6>
        <h3 class="display-6 fw-bold mb-0">
          ₱{{ "%.2f"|format((sales_data|sum(attribute='total_revenue') /
          sales_data|length) if sales_data|length > 0 else 0) }}
        </h3>
      </div>
    </div>
  </div>
</div>

<!-- Product Sales Analysis -->
<div class="row">
  <div class="col-md-8">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-chart-bar"></i> Product Sales Analysis
        </h5>
      </div>
      <div class="card-body">
        {% if sales_data %}
        <div class="table-responsive">
          <table class="table table-striped">
            <thead class="table-dark">
              <tr>
                <th>Product</th>
                <th>Quantity Sold</th>
                <th>Revenue</th>
                <th>Avg. Price</th>
                <th>Performance</th>
              </tr>
            </thead>
            <tbody>
              {% for item in sales_data|sort(attribute='total_revenue',
              reverse=true) %}
              <tr>
                <td><strong>{{ item.name }}</strong></td>
                <td>{{ item.total_sold }}</td>
                <td class="text-success">
                  ₱{{ "{:,.2f}".format(item.total_revenue) }}
                </td>
                <td>
                  ₱{{ "{:,.2f}".format(item.total_revenue / item.total_sold) }}
                </td>
                <td>
                  {% set percentage = (item.total_revenue /
                  (sales_data|sum(attribute='total_revenue')) * 100) if
                  sales_data|sum(attribute='total_revenue') > 0 else 0 %}
                  <div class="progress" style="height: 20px">
                    <div
                      class="progress-bar bg-success"
                      role="progressbar"
                      style="width: {{ percentage }}%"
                      aria-valuenow="{{ percentage }}"
                      aria-valuemin="0"
                      aria-valuemax="100"
                    >
                      {{ "{:,.1f}".format(percentage) }}%
                    </div>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        {% else %}
        <div class="text-center py-5">
          <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
          <h4 class="text-muted">No sales data available</h4>
          <p class="text-muted">Start making sales to see analytics here.</p>
          <a href="{{ url_for('pos') }}" class="btn btn-primary">
            <i class="fas fa-cash-register"></i> Go to POS
          </a>
        </div>
        {% endif %}
      </div>
    </div>
  </div>

  <div class="col-md-4">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-trophy"></i> Top Performers</h5>
      </div>
      <div class="card-body">
        {% if sales_data %} {% for item in
        sales_data|sort(attribute='total_revenue', reverse=true) %} {% if
        loop.index <= 5 %}
        <div class="d-flex justify-content-between align-items-center mb-2">
          <div>
            <strong>{{ item.name }}</strong><br />
            <small class="text-muted">{{ item.total_sold }} units</small>
          </div>
          <div class="text-end">
            <span class="badge bg-success"
              >₱{{ "%.2f"|format(item.total_revenue) }}</span
            >
          </div>
        </div>
        {% if not loop.last %}
        <hr class="my-2" />
        {% endif %} {% endif %} {% endfor %} {% else %}
        <p class="text-muted text-center">No data available</p>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- Daily Sales Trend -->
{% if daily_sales %}
<div class="row mt-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-calendar-alt"></i> Daily Sales Trend (Last 30 Days)
        </h5>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead class="table-dark">
              <tr>
                <th>Date</th>
                <th>Items Sold</th>
                <th>Revenue</th>
                <th>Trend</th>
              </tr>
            </thead>
            <tbody>
              {% for day in daily_sales %}
              <tr>
                <td>
                  {{ day.sale_date.strftime('%Y-%m-%d') if day.sale_date else
                  'N/A' }}
                </td>
                <td>{{ day.daily_quantity }}</td>
                <td class="text-success">
                  ₱{{ "{:,.2f}".format(day.daily_revenue) }}
                </td>
                <td>
                  {% set max_revenue =
                  daily_sales|map(attribute='daily_revenue')|max %} {% set
                  percentage = (day.daily_revenue / max_revenue * 100) if
                  max_revenue > 0 else 0 %}
                  <div class="progress" style="height: 15px">
                    <div
                      class="progress-bar bg-info"
                      role="progressbar"
                      style="width: {{ percentage }}%"
                    ></div>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
{% endif %} {% endblock %}
