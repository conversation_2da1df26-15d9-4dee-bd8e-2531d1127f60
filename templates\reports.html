{% extends "base.html" %} {% block title %}Reports - Frozen Foods POS{% endblock
%} {% block content %}
<div class="row">
  <div class="col-12">
    <h1 class="mb-4"><i class="fas fa-chart-bar"></i> Reports & Analytics</h1>
  </div>
</div>

<div class="row">
  <div class="col-md-4 mb-4">
    <div class="card h-100">
      <div class="card-body text-center">
        <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
        <h5 class="card-title">Sales Report</h5>
        <p class="card-text">
          View detailed sales analytics, revenue trends, and product
          performance.
        </p>
        <a href="{{ url_for('sales_report') }}" class="btn btn-primary">
          <i class="fas fa-eye"></i> View Sales Report
        </a>
      </div>
    </div>
  </div>

  <div class="col-md-4 mb-4">
    <div class="card h-100">
      <div class="card-body text-center">
        <i class="fas fa-warehouse fa-3x text-success mb-3"></i>
        <h5 class="card-title">Inventory Report</h5>
        <p class="card-text">
          Monitor stock levels, identify low inventory, and track product
          movement.
        </p>
        <a href="{{ url_for('inventory_report') }}" class="btn btn-success">
          <i class="fas fa-eye"></i> View Inventory Report
        </a>
      </div>
    </div>
  </div>

  <div class="col-md-4 mb-4">
    <div class="card h-100">
      <div class="card-body text-center">
        <i class="fas fa-receipt fa-3x text-info mb-3"></i>
        <h5 class="card-title">Transaction History</h5>
        <p class="card-text">
          Browse through all transactions, search by date, and view transaction
          details.
        </p>
        <a href="{{ url_for('transaction_history') }}" class="btn btn-info">
          <i class="fas fa-eye"></i> View Transactions
        </a>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6 mb-4">
    <div class="card h-100">
      <div class="card-body text-center">
        <i class="fas fa-filter fa-3x text-primary mb-3"></i>
        <h5 class="card-title">Advanced Sales Filter</h5>
        <p class="card-text">
          Filter sales by date range, analyze trends, and get detailed insights.
        </p>
        <a href="{{ url_for('sales_filter') }}" class="btn btn-primary">
          <i class="fas fa-search"></i> Filter Sales
        </a>
      </div>
    </div>
  </div>

  <div class="col-md-6 mb-4">
    <div class="card h-100">
      <div class="card-body text-center">
        <i class="fas fa-exchange-alt fa-3x text-secondary mb-3"></i>
        <h5 class="card-title">Inventory Movements</h5>
        <p class="card-text">
          Track product movements, receipts, and stock changes over time.
        </p>
        <a
          href="{{ url_for('inventory_movements') }}"
          class="btn btn-secondary"
        >
          <i class="fas fa-eye"></i> View Movements
        </a>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6 mb-4">
    <div class="card h-100">
      <div class="card-body text-center">
        <i class="fas fa-users fa-3x text-warning mb-3"></i>
        <h5 class="card-title">Cashier Performance</h5>
        <p class="card-text">
          View cashier rankings, sales performance, and staff analytics.
        </p>
        <a href="{{ url_for('cashier_performance') }}" class="btn btn-warning">
          <i class="fas fa-trophy"></i> View Performance
        </a>
      </div>
    </div>
  </div>

  <div class="col-md-6 mb-4">
    <div class="card h-100">
      <div class="card-body text-center">
        <i class="fas fa-edit fa-3x text-danger mb-3"></i>
        <h5 class="card-title">Transaction Date Editor</h5>
        <p class="card-text">
          Edit transaction dates for historical data entry and corrections.
        </p>
        <a href="{{ url_for('transaction_editor') }}" class="btn btn-danger">
          <i class="fas fa-calendar-edit"></i> Edit Dates
        </a>
      </div>
    </div>
  </div>
</div>

<div class="row mt-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-info-circle"></i> Report Information
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-4">
            <h6 class="text-primary">Sales Report Features:</h6>
            <ul class="list-unstyled">
              <li>
                <i class="fas fa-check text-success"></i> Product-wise sales
                analysis
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Daily revenue tracking
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Top-selling products
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Sales trends over time
              </li>
            </ul>
          </div>
          <div class="col-md-4">
            <h6 class="text-success">Inventory Report Features:</h6>
            <ul class="list-unstyled">
              <li>
                <i class="fas fa-check text-success"></i> Current stock levels
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Low stock alerts
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Stock movement
                tracking
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Inventory valuation
              </li>
            </ul>
          </div>
          <div class="col-md-4">
            <h6 class="text-info">Transaction History Features:</h6>
            <ul class="list-unstyled">
              <li>
                <i class="fas fa-check text-success"></i> Complete transaction
                log
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Transaction details
                view
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Date-wise filtering
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Transaction search
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
