{% extends "base.html" %} {% block title %}Reports - Frozen Foods POS{% endblock
%} {% block content %}
<div class="row">
  <div class="col-12">
    <h1 class="mb-4"><i class="fas fa-chart-bar"></i> Reports & Analytics</h1>
  </div>
</div>

<div class="row">
  <div class="col-md-3 mb-4">
    <div class="card h-100">
      <div class="card-body text-center d-flex flex-column">
        <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
        <h5 class="card-title">Sales Report</h5>
        <p class="card-text flex-grow-1">
          View detailed sales analytics, revenue trends, and product
          performance.
        </p>
        <a href="{{ url_for('sales_report') }}" class="btn btn-primary mt-auto">
          <i class="fas fa-eye"></i> View Sales Report
        </a>
      </div>
    </div>
  </div>

  <div class="col-md-3 mb-4">
    <div class="card h-100">
      <div class="card-body text-center d-flex flex-column">
        <i class="fas fa-warehouse fa-3x text-success mb-3"></i>
        <h5 class="card-title">Inventory Report</h5>
        <p class="card-text flex-grow-1">
          Monitor stock levels, identify low inventory, and track product
          movement.
        </p>
        <a
          href="{{ url_for('inventory_report') }}"
          class="btn btn-success mt-auto"
        >
          <i class="fas fa-eye"></i> View Inventory Report
        </a>
      </div>
    </div>
  </div>

  <div class="col-md-3 mb-4">
    <div class="card h-100">
      <div class="card-body text-center d-flex flex-column">
        <i class="fas fa-receipt fa-3x text-info mb-3"></i>
        <h5 class="card-title">Transaction History</h5>
        <p class="card-text flex-grow-1">
          Browse through all transactions, search by date, and view transaction
          details.
        </p>
        <a
          href="{{ url_for('transaction_history') }}"
          class="btn btn-info mt-auto"
        >
          <i class="fas fa-eye"></i> View Transactions
        </a>
      </div>
    </div>
  </div>

  <div class="col-md-3 mb-4">
    <div class="card h-100">
      <div class="card-body text-center d-flex flex-column">
        <i class="fas fa-plus-circle fa-3x text-warning mb-3"></i>
        <h5 class="card-title">Sales History</h5>
        <p class="card-text flex-grow-1">
          Add new transactions manually and manage individual sales entries with
          full integration.
        </p>
        <a
          href="{{ url_for('sales_history') }}"
          class="btn btn-warning mt-auto"
        >
          <i class="fas fa-plus"></i> Manage Sales History
        </a>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6 mb-4">
    <div class="card h-100">
      <div class="card-body text-center d-flex flex-column">
        <i class="fas fa-filter fa-3x text-primary mb-3"></i>
        <h5 class="card-title">Advanced Sales Filter</h5>
        <p class="card-text flex-grow-1">
          Filter sales by date range, analyze trends, and get detailed insights.
        </p>
        <a href="{{ url_for('sales_filter') }}" class="btn btn-primary mt-auto">
          <i class="fas fa-search"></i> Filter Sales
        </a>
      </div>
    </div>
  </div>

  <div class="col-md-6 mb-4">
    <div class="card h-100">
      <div class="card-body text-center d-flex flex-column">
        <i class="fas fa-exchange-alt fa-3x text-secondary mb-3"></i>
        <h5 class="card-title">Inventory Movements</h5>
        <p class="card-text flex-grow-1">
          Track product movements, receipts, and stock changes over time.
        </p>
        <a
          href="{{ url_for('inventory_movements') }}"
          class="btn btn-secondary mt-auto"
        >
          <i class="fas fa-eye"></i> View Movements
        </a>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6 mb-4">
    <div class="card h-100">
      <div class="card-body text-center d-flex flex-column">
        <i class="fas fa-users fa-3x text-warning mb-3"></i>
        <h5 class="card-title">Cashier Performance</h5>
        <p class="card-text flex-grow-1">
          View cashier rankings, sales performance, and staff analytics.
        </p>
        <a
          href="{{ url_for('cashier_performance') }}"
          class="btn btn-warning mt-auto"
        >
          <i class="fas fa-trophy"></i> View Performance
        </a>
      </div>
    </div>
  </div>

  <div class="col-md-6 mb-4">
    <div class="card h-100">
      <div class="card-body text-center d-flex flex-column">
        <i class="fas fa-edit fa-3x text-danger mb-3"></i>
        <h5 class="card-title">Transaction Date Editor</h5>
        <p class="card-text flex-grow-1">
          Edit transaction dates for historical data entry and corrections.
        </p>
        <a
          href="{{ url_for('transaction_editor') }}"
          class="btn btn-danger mt-auto"
        >
          <i class="fas fa-calendar-edit"></i> Edit Dates
        </a>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-md-6 mb-4">
    <div class="card h-100">
      <div class="card-body text-center d-flex flex-column">
        <i class="fas fa-cog fa-3x text-dark mb-3"></i>
        <h5 class="card-title">System Settings</h5>
        <p class="card-text flex-grow-1">
          Configure reseller discounts, pricing settings, and system
          preferences.
        </p>
        <a href="{{ url_for('settings') }}" class="btn btn-dark mt-auto">
          <i class="fas fa-cog"></i> Manage Settings
        </a>
      </div>
    </div>
  </div>
</div>

<div class="row mt-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-info-circle"></i> Report Information
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-3">
            <h6 class="text-primary">Sales Report Features:</h6>
            <ul class="list-unstyled">
              <li>
                <i class="fas fa-check text-success"></i> Product-wise sales
                analysis
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Daily revenue tracking
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Top-selling products
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Sales trends over time
              </li>
            </ul>
          </div>
          <div class="col-md-3">
            <h6 class="text-success">Inventory Report Features:</h6>
            <ul class="list-unstyled">
              <li>
                <i class="fas fa-check text-success"></i> Current stock levels
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Low stock alerts
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Stock movement
                tracking
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Inventory valuation
              </li>
            </ul>
          </div>
          <div class="col-md-3">
            <h6 class="text-info">Transaction History Features:</h6>
            <ul class="list-unstyled">
              <li>
                <i class="fas fa-check text-success"></i> Complete transaction
                log
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Transaction details
                view
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Date-wise filtering
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Transaction search
              </li>
            </ul>
          </div>
          <div class="col-md-3">
            <h6 class="text-warning">Sales History Features:</h6>
            <ul class="list-unstyled">
              <li>
                <i class="fas fa-check text-success"></i> Manual transaction
                entry
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Auto-generated
                transaction IDs
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Cashier & payment
                tracking
              </li>
              <li>
                <i class="fas fa-check text-success"></i> Full system
                integration
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
