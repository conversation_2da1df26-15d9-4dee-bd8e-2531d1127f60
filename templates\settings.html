{% extends "base.html" %} 
{% block title %}Settings - ATE MEG's FROZEN FOODS{% endblock %} 
{% block content %}

<!-- Settings Header -->
<div class="row mb-4">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <h1 class="display-6 fw-bold text-primary mb-2">
          <i class="fas fa-cog me-3"></i>System Settings
        </h1>
        <p class="text-muted fs-5">Configure system preferences and pricing</p>
      </div>
      <div>
        <a href="{{ url_for('reports') }}" class="btn btn-outline-primary">
          <i class="fas fa-arrow-left me-2"></i>Back to Reports
        </a>
      </div>
    </div>
  </div>
</div>

<!-- Reseller Discount Settings -->
<div class="row mb-4">
  <div class="col-md-6">
    <div class="card">
      <div class="card-header bg-warning text-dark">
        <h5 class="mb-0">
          <i class="fas fa-percentage me-2"></i>Reseller Discount Settings
        </h5>
      </div>
      <div class="card-body">
        <form id="resellerDiscountForm">
          <div class="mb-3">
            <label for="reseller_discount" class="form-label">
              <strong>Reseller Discount Amount (₱)</strong>
            </label>
            <div class="input-group">
              <span class="input-group-text">₱</span>
              <input 
                type="number" 
                class="form-control" 
                id="reseller_discount" 
                name="reseller_discount" 
                value="{{ reseller_discount }}"
                min="0" 
                step="0.01" 
                required>
            </div>
            <div class="form-text">
              Amount to subtract from regular price for reseller customers
            </div>
          </div>

          <div class="mb-3">
            <div class="alert alert-info">
              <h6><i class="fas fa-info-circle me-2"></i>How it works:</h6>
              <ul class="mb-0">
                <li>When "Reseller" is selected as customer type, this amount is deducted from each product's price</li>
                <li>Example: If regular price is ₱110 and discount is ₱5, reseller pays ₱105</li>
                <li>Minimum price is ₱0 (price cannot go negative)</li>
                <li>This applies to both POS and Sales History transactions</li>
              </ul>
            </div>
          </div>

          <div class="d-flex gap-2">
            <button type="submit" class="btn btn-warning">
              <i class="fas fa-save me-2"></i>Update Discount
            </button>
            <button type="button" class="btn btn-outline-secondary" id="reset-discount">
              <i class="fas fa-undo me-2"></i>Reset to Default (₱5.00)
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <div class="col-md-6">
    <div class="card">
      <div class="card-header bg-info text-white">
        <h5 class="mb-0">
          <i class="fas fa-calculator me-2"></i>Price Preview
        </h5>
      </div>
      <div class="card-body">
        <h6>Sample Price Calculations:</h6>
        <div class="table-responsive">
          <table class="table table-sm">
            <thead>
              <tr>
                <th>Regular Price</th>
                <th>Reseller Price</th>
                <th>Savings</th>
              </tr>
            </thead>
            <tbody id="price-preview">
              <!-- Will be populated by JavaScript -->
            </tbody>
          </table>
        </div>
        <small class="text-muted">
          Preview updates automatically when you change the discount amount
        </small>
      </div>
    </div>
  </div>
</div>

<!-- System Information -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header bg-secondary text-white">
        <h5 class="mb-0">
          <i class="fas fa-info-circle me-2"></i>System Information
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-4">
            <h6 class="text-secondary">Date Format:</h6>
            <p>All dates are now displayed as YYYY-MM-DD (date only)</p>
          </div>
          <div class="col-md-4">
            <h6 class="text-secondary">Customer Types:</h6>
            <ul class="list-unstyled">
              <li><strong>Default:</strong> Regular pricing</li>
              <li><strong>Reseller:</strong> Discounted pricing</li>
            </ul>
          </div>
          <div class="col-md-4">
            <h6 class="text-secondary">Integration:</h6>
            <p>Reseller pricing works in both POS and Sales History systems</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const discountInput = document.getElementById('reseller_discount');
    const form = document.getElementById('resellerDiscountForm');
    const resetBtn = document.getElementById('reset-discount');
    
    // Sample prices for preview
    const samplePrices = [110, 90, 80, 75, 60];
    
    // Update price preview when discount changes
    function updatePricePreview() {
        const discount = parseFloat(discountInput.value) || 0;
        const tbody = document.getElementById('price-preview');
        
        tbody.innerHTML = '';
        samplePrices.forEach(price => {
            const resellerPrice = Math.max(0, price - discount);
            const savings = price - resellerPrice;
            
            const row = tbody.insertRow();
            row.innerHTML = `
                <td>₱${price.toFixed(2)}</td>
                <td class="text-success">₱${resellerPrice.toFixed(2)}</td>
                <td class="text-warning">₱${savings.toFixed(2)}</td>
            `;
        });
    }
    
    // Initial preview update
    updatePricePreview();
    
    // Update preview when discount input changes
    discountInput.addEventListener('input', updatePricePreview);
    
    // Handle form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const discount = parseFloat(discountInput.value);
        
        if (discount < 0) {
            alert('Discount cannot be negative');
            return;
        }
        
        // Submit to API
        fetch('/api/update_reseller_discount', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                discount: discount
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                // Update the preview
                updatePricePreview();
            } else {
                alert(`Error: ${data.message}`);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the discount.');
        });
    });
    
    // Handle reset button
    resetBtn.addEventListener('click', function() {
        if (confirm('Reset reseller discount to default ₱5.00?')) {
            discountInput.value = '5.00';
            updatePricePreview();
        }
    });
});
</script>

{% endblock %}
