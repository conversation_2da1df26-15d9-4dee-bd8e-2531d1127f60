{% extends "base.html" %} {% block title %}Dashboard - ATE MEG's FROZEN FOODS{%
endblock %} {% block content %}
<!-- Enhanced Dashboard Header -->
<div class="row mb-4">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <h1 class="display-5 fw-bold text-primary mb-2">
          <i class="fas fa-chart-line me-3"></i>Business Dashboard
        </h1>
        <p class="text-muted fs-6 mb-0">
          Real-time overview • {{ current_date }}
        </p>
      </div>
      <div class="text-end">
        <div class="badge bg-success fs-6 px-3 py-2 rounded-pill">
          <i class="fas fa-circle me-2"></i>Live
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Today's Performance Section -->
<div class="row mb-4">
  <div class="col-12">
    <h5 class="text-primary fw-bold mb-3">
      <i class="fas fa-calendar-day me-2"></i>Today's Performance
    </h5>
  </div>
</div>

<!-- Key Performance Indicators -->
<div class="row g-3 mb-4">
  <!-- Today's Revenue -->
  <div class="col-lg-3 col-md-6">
    <div class="card border-0 shadow-sm h-100">
      <div class="card-body">
        <div class="d-flex align-items-center">
          <div class="flex-shrink-0">
            <div class="bg-success bg-opacity-10 rounded-3 p-3">
              <i class="fas fa-money-bill-wave text-success fa-lg"></i>
            </div>
          </div>
          <div class="flex-grow-1 ms-3">
            <h6 class="text-muted mb-1 small">Today's Revenue</h6>
            <h4 class="mb-0 fw-bold">
              ₱{{ "{:,.2f}".format(today_revenue or 0) }}
            </h4>
            <small class="text-success">
              <i class="fas fa-arrow-up me-1"></i>+12% vs yesterday
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Today's Transactions -->
  <div class="col-lg-3 col-md-6">
    <div class="card border-0 shadow-sm h-100">
      <div class="card-body">
        <div class="d-flex align-items-center">
          <div class="flex-shrink-0">
            <div class="bg-primary bg-opacity-10 rounded-3 p-3">
              <i class="fas fa-receipt text-primary fa-lg"></i>
            </div>
          </div>
          <div class="flex-grow-1 ms-3">
            <h6 class="text-muted mb-1 small">Today's Sales</h6>
            <h4 class="mb-0 fw-bold">{{ today_transactions or 0 }}</h4>
            <small class="text-primary">
              <i class="fas fa-clock me-1"></i>{{ today_items_sold or 0 }} items
              sold
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Outstanding Credits -->
  <div class="col-lg-3 col-md-6">
    <div class="card border-0 shadow-sm h-100">
      <div class="card-body">
        <div class="d-flex align-items-center">
          <div class="flex-shrink-0">
            <div class="bg-warning bg-opacity-10 rounded-3 p-3">
              <i class="fas fa-credit-card text-warning fa-lg"></i>
            </div>
          </div>
          <div class="flex-grow-1 ms-3">
            <h6 class="text-muted mb-1 small">Outstanding Credit</h6>
            <h4 class="mb-0 fw-bold">
              ₱{{ "{:,.2f}".format(total_outstanding_credit or 0) }}
            </h4>
            <small class="text-warning">
              <i class="fas fa-users me-1"></i>{{ customers_with_debt or 0 }}
              customers
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Critical Stock -->
  <div class="col-lg-3 col-md-6">
    <div class="card border-0 shadow-sm h-100">
      <div class="card-body">
        <div class="d-flex align-items-center">
          <div class="flex-shrink-0">
            <div class="bg-danger bg-opacity-10 rounded-3 p-3">
              <i class="fas fa-exclamation-triangle text-danger fa-lg"></i>
            </div>
          </div>
          <div class="flex-grow-1 ms-3">
            <h6 class="text-muted mb-1 small">Critical Stock</h6>
            <h4 class="mb-0 fw-bold">{{ out_of_stock_count or 0 }}</h4>
            <small class="text-danger">
              <i class="fas fa-box me-1"></i>{{ low_stock_count or 0 }} low
              stock
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Business Insights Section -->
<div class="row mb-4">
  <div class="col-12">
    <h5 class="text-primary fw-bold mb-3">
      <i class="fas fa-lightbulb me-2"></i>Business Insights
    </h5>
  </div>
</div>

<div class="row g-3 mb-4">
  <!-- Quick Actions -->
  <div class="col-lg-4">
    <div class="card border-0 shadow-sm h-100">
      <div class="card-header bg-primary text-white">
        <h6 class="mb-0"><i class="fas fa-rocket me-2"></i>Quick Actions</h6>
      </div>
      <div class="card-body">
        <div class="d-grid gap-2">
          <a href="{{ url_for('pos') }}" class="btn btn-primary">
            <i class="fas fa-cash-register me-2"></i>Start New Sale
          </a>
          <a
            href="{{ url_for('add_product') }}"
            class="btn btn-outline-success"
          >
            <i class="fas fa-plus me-2"></i>Add Product
          </a>
          <a
            href="{{ url_for('inventory_report') }}"
            class="btn btn-outline-info"
          >
            <i class="fas fa-warehouse me-2"></i>Check Inventory
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Critical Alerts -->
  <div class="col-lg-4">
    <div class="card border-0 shadow-sm h-100">
      <div class="card-header bg-danger text-white">
        <h6 class="mb-0">
          <i class="fas fa-exclamation-triangle me-2"></i>Critical Alerts
        </h6>
      </div>
      <div class="card-body">
        {% if low_stock_products %}
        <div class="list-group list-group-flush">
          {% for product in low_stock_products[:4] %}
          <div
            class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 py-2"
          >
            <span class="small">{{ product.name }}</span>
            <span class="badge bg-danger rounded-pill"
              >{{ product.stock }}</span
            >
          </div>
          {% endfor %}
        </div>
        {% if low_stock_products|length > 4 %}
        <small class="text-muted"
          >+{{ low_stock_products|length - 4 }} more items</small
        >
        {% endif %} {% else %}
        <div class="text-center py-3">
          <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
          <p class="text-muted mb-0 small">All stock levels good!</p>
        </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Top Products Today -->
  <div class="col-lg-4">
    <div class="card border-0 shadow-sm h-100">
      <div class="card-header bg-success text-white">
        <h6 class="mb-0"><i class="fas fa-star me-2"></i>Top Sellers Today</h6>
      </div>
      <div class="card-body">
        {% if top_products_today %}
        <div class="list-group list-group-flush">
          {% for product in top_products_today[:4] %}
          <div
            class="list-group-item d-flex justify-content-between align-items-center border-0 px-0 py-2"
          >
            <span class="small">{{ product.name }}</span>
            <span class="badge bg-success rounded-pill"
              >{{ product.sold_today or 0 }}</span
            >
          </div>
          {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-3">
          <i class="fas fa-chart-line text-muted fa-2x mb-2"></i>
          <p class="text-muted mb-0 small">No sales data yet today</p>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- Financial Summary -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card border-0 shadow-sm">
      <div class="card-header bg-light">
        <h6 class="mb-0 text-primary fw-bold">
          <i class="fas fa-chart-pie me-2"></i>Financial Summary
        </h6>
      </div>
      <div class="card-body">
        <div class="row text-center">
          <div class="col-md-3">
            <h5 class="text-success mb-1">
              ₱{{ "{:,.2f}".format(total_revenue or 0) }}
            </h5>
            <small class="text-muted">Total Revenue</small>
          </div>
          <div class="col-md-3">
            <h5 class="text-primary mb-1">
              ₱{{ "{:,.2f}".format(total_profit or 0) }}
            </h5>
            <small class="text-muted">Total Profit</small>
          </div>
          <div class="col-md-3">
            <h5 class="text-info mb-1">{{ total_products or 0 }}</h5>
            <small class="text-muted">Total Products</small>
          </div>
          <div class="col-md-3">
            <h5 class="text-warning mb-1">{{ total_transactions or 0 }}</h5>
            <small class="text-muted">Total Transactions</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
