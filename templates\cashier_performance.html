{% extends "base.html" %} {% block title %}Cashier Performance - Frozen Foods
POS{% endblock %} {% block content %}
<div class="row">
  <div class="col-12">
    <h1 class="mb-4">
      <i class="fas fa-chart-line"></i> Cashier Performance Dashboard
    </h1>
  </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
  <div class="col-md-3">
    <div class="card bg-primary text-white">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h6 class="card-title">Total Sales</h6>
            <h4>₱{{ "{:,.2f}".format(total_company_sales) }}</h4>
          </div>
          <div class="align-self-center">
            <i class="fas fa-money-bill-wave fa-2x"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-success text-white">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h6 class="card-title">Total Profit</h6>
            <h4>₱{{ "{:,.2f}".format(total_company_profit) }}</h4>
          </div>
          <div class="align-self-center">
            <i class="fas fa-chart-line fa-2x"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-info text-white">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h6 class="card-title">Total Transactions</h6>
            <h4>{{ total_company_transactions }}</h4>
          </div>
          <div class="align-self-center">
            <i class="fas fa-receipt fa-2x"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card bg-warning text-white">
      <div class="card-body">
        <div class="d-flex justify-content-between">
          <div>
            <h6 class="card-title">Items Sold</h6>
            <h4>{{ total_company_items }}</h4>
          </div>
          <div class="align-self-center">
            <i class="fas fa-box fa-2x"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Top Performers -->
<div class="row mb-2">
  <div class="col-md-4">
    <div class="card mb-3">
      <div class="card-header bg-primary text-white">
        <h6 class="mb-0"><i class="fas fa-trophy"></i> Top Sales Performer</h6>
      </div>
      <div class="card-body">
        {% if cashier_performance %} {% set top_sales = cashier_performance[0]
        %}
        <h5>🥇 {{ top_sales.cashier.name }}</h5>
        <p class="mb-1">
          <strong>Sales:</strong> ₱{{ "{:,.2f}".format(top_sales.total_sales) }}
        </p>
        <p class="mb-1">
          <strong>Transactions:</strong> {{ top_sales.total_transactions }}
        </p>
        <p class="mb-0">
          <strong>Avg per Transaction:</strong> ₱{{
          "{:,.2f}".format(top_sales.avg_transaction_value) }}
        </p>
        {% else %}
        <p class="text-muted">No data available</p>
        {% endif %}
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="card mb-3">
      <div class="card-header bg-success text-white">
        <h6 class="mb-0">
          <i class="fas fa-chart-line"></i> Top Profit Generator
        </h6>
      </div>
      <div class="card-body">
        {% if cashier_performance %} {% set top_profit =
        cashier_performance|sort(attribute='total_profit', reverse=true)|first
        %}
        <h5>🥇 {{ top_profit.cashier.name }}</h5>
        <p class="mb-1">
          <strong>Profit:</strong> ₱{{ "{:,.2f}".format(top_profit.total_profit)
          }}
        </p>
        <p class="mb-1">
          <strong>Margin:</strong> {{ "{:,.1f}".format(top_profit.profit_margin)
          }}%
        </p>
        <p class="mb-0">
          <strong>Sales:</strong> ₱{{ "{:,.2f}".format(top_profit.total_sales)
          }}
        </p>
        {% else %}
        <p class="text-muted">No data available</p>
        {% endif %}
      </div>
    </div>
  </div>
  <div class="col-md-4">
    <div class="card mb-3">
      <div class="card-header bg-info text-white">
        <h6 class="mb-0"><i class="fas fa-bolt"></i> Most Active Cashier</h6>
      </div>
      <div class="card-body">
        {% if cashier_performance %} {% set most_active =
        cashier_performance|sort(attribute='total_transactions',
        reverse=true)|first %}
        <h5>🥇 {{ most_active.cashier.name }}</h5>
        <p class="mb-1">
          <strong>Transactions:</strong> {{ most_active.total_transactions }}
        </p>
        <p class="mb-1">
          <strong>Items Sold:</strong> {{ most_active.total_items_sold }}
        </p>
        <p class="mb-0">
          <strong>Avg Items/Transaction:</strong> {{
          "{:,.1f}".format(most_active.avg_items_per_transaction) }}
        </p>
        {% else %}
        <p class="text-muted">No data available</p>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- Detailed Performance Table -->
<div class="row mt-2">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-table"></i> Detailed Cashier Performance
        </h5>
      </div>
      <div class="card-body">
        {% if cashier_performance %}
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead class="table-dark">
              <tr>
                <th>Rank</th>
                <th>Cashier</th>
                <th>Total Sales</th>
                <th>Total Profit</th>
                <th>Profit Margin</th>
                <th>Transactions</th>
                <th>Items Sold</th>
                <th>Avg Transaction</th>
                <th>Avg Items/Trans</th>
                <th>Performance</th>
              </tr>
            </thead>
            <tbody>
              {% for performance in cashier_performance %}
              <tr>
                <td>
                  {% if performance.sales_rank == 1 %}
                  <span class="badge bg-warning text-dark"
                    >🥇 #{{ performance.sales_rank }}</span
                  >
                  {% elif performance.sales_rank == 2 %}
                  <span class="badge bg-secondary"
                    >🥈 #{{ performance.sales_rank }}</span
                  >
                  {% elif performance.sales_rank == 3 %}
                  <span class="badge bg-warning text-dark"
                    >🥉 #{{ performance.sales_rank }}</span
                  >
                  {% else %}
                  <span class="badge bg-light text-dark"
                    >#{{ performance.sales_rank }}</span
                  >
                  {% endif %}
                </td>
                <td>
                  <strong>{{ performance.cashier.name }}</strong>
                  {% if performance.last_transaction %}
                  <br /><small class="text-muted"
                    >Last active: {{
                    performance.last_transaction.strftime('%Y-%m-%d') }}</small
                  >
                  {% endif %}
                </td>
                <td>
                  <strong
                    >₱{{ "{:,.2f}".format(performance.total_sales) }}</strong
                  >
                  {% if total_company_sales > 0 %}
                  <br /><small class="text-muted"
                    >{{ "{:,.1f}".format(performance.total_sales /
                    total_company_sales * 100) }}% of total</small
                  >
                  {% endif %}
                </td>
                <td>
                  <strong
                    >₱{{ "{:,.2f}".format(performance.total_profit) }}</strong
                  >
                  <br /><small class="badge bg-success"
                    >Rank #{{ performance.profit_rank }}</small
                  >
                </td>
                <td>
                  <span
                    class="badge {% if performance.profit_margin >= 25 %}bg-success{% elif performance.profit_margin >= 20 %}bg-warning{% else %}bg-danger{% endif %}"
                  >
                    {{ "{:,.1f}".format(performance.profit_margin) }}%
                  </span>
                </td>
                <td>
                  <strong>{{ performance.total_transactions }}</strong>
                  <br /><small class="badge bg-info"
                    >Rank #{{ performance.activity_rank }}</small
                  >
                </td>
                <td>{{ performance.total_items_sold }}</td>
                <td>
                  ₱{{ "{:,.2f}".format(performance.avg_transaction_value) }}
                </td>
                <td>
                  {{ "{:,.1f}".format(performance.avg_items_per_transaction) }}
                </td>
                <td>
                  {% set overall_score = (performance.sales_rank +
                  performance.profit_rank + performance.activity_rank) / 3 %} {%
                  if overall_score <= 1.5 %}
                  <span class="badge bg-success">⭐⭐⭐ Excellent</span>
                  {% elif overall_score <= 2.5 %}
                  <span class="badge bg-warning">⭐⭐ Good</span>
                  {% else %}
                  <span class="badge bg-secondary">⭐ Average</span>
                  {% endif %}
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        {% else %}
        <div class="text-center py-4">
          <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
          <h5 class="text-muted">No Performance Data Available</h5>
          <p class="text-muted">
            Start processing transactions to see cashier performance metrics.
          </p>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- Performance Charts Section -->
{% if cashier_performance %}
<div class="row mt-3">
  <div class="col-md-6">
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0">
          <i class="fas fa-chart-pie"></i> Sales Distribution
        </h6>
      </div>
      <div class="card-body">
        <canvas id="salesChart" width="400" height="200"></canvas>
      </div>
    </div>
  </div>
  <div class="col-md-6">
    <div class="card">
      <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Profit Comparison</h6>
      </div>
      <div class="card-body">
        <canvas id="profitChart" width="400" height="200"></canvas>
      </div>
    </div>
  </div>
</div>
{% endif %} {% endblock %} {% block scripts %} {% if cashier_performance %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  // Sales Distribution Pie Chart
  const salesCtx = document.getElementById('salesChart').getContext('2d');
  const salesChart = new Chart(salesCtx, {
      type: 'pie',
      data: {
          labels: [{% for p in cashier_performance %}'{{ p.cashier.name }}'{% if not loop.last %},{% endif %}{% endfor %}],
          datasets: [{
              data: [{% for p in cashier_performance %}{{ p.total_sales }}{% if not loop.last %},{% endif %}{% endfor %}],
              backgroundColor: [
                  '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'
              ]
          }]
      },
      options: {
          responsive: true,
          plugins: {
              legend: {
                  position: 'bottom'
              }
          }
      }
  });

  // Profit Comparison Bar Chart
  const profitCtx = document.getElementById('profitChart').getContext('2d');
  const profitChart = new Chart(profitCtx, {
      type: 'bar',
      data: {
          labels: [{% for p in cashier_performance %}'{{ p.cashier.name }}'{% if not loop.last %},{% endif %}{% endfor %}],
          datasets: [{
              label: 'Profit (₱)',
              data: [{% for p in cashier_performance %}{{ p.total_profit }}{% if not loop.last %},{% endif %}{% endfor %}],
              backgroundColor: '#28a745',
              borderColor: '#1e7e34',
              borderWidth: 1
          }]
      },
      options: {
          responsive: true,
          scales: {
              y: {
                  beginAtZero: true,
                  ticks: {
                      callback: function(value) {
                          return '₱' + value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                      }
                  }
              }
          },
          plugins: {
              legend: {
                  display: false
              }
          }
      }
  });
</script>
{% endif %} {% endblock %}
