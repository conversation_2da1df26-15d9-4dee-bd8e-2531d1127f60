#!/usr/bin/env python3
"""
Migrate database to add capital_ratio column
"""

from app import app, db, Product
import sqlite3
import os

def migrate_database():
    """Add capital_ratio column to existing database"""
    with app.app_context():
        print("🔧 Migrating database to add capital_ratio column...")
        
        # Check if column already exists
        try:
            # Try to access capital_ratio - if it works, column exists
            test_product = Product.query.first()
            if test_product and hasattr(test_product, 'capital_ratio'):
                print("✅ capital_ratio column already exists")
                return True
        except Exception:
            pass
        
        try:
            # Get database path
            db_path = 'frozen_foods_pos.db'
            if not os.path.exists(db_path):
                print("❌ Database file not found")
                return False
            
            # Connect directly to SQLite
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Add the column
            cursor.execute('ALTER TABLE product ADD COLUMN capital_ratio FLOAT DEFAULT 0.7')
            conn.commit()
            
            print("✅ capital_ratio column added successfully")
            
            # Update existing products with default capital ratio
            cursor.execute('UPDATE product SET capital_ratio = 0.7 WHERE capital_ratio IS NULL')
            conn.commit()
            
            print("✅ Updated existing products with default capital ratio (70%)")
            
            conn.close()
            return True
            
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e):
                print("✅ capital_ratio column already exists")
                return True
            else:
                print(f"❌ Error adding column: {e}")
                return False
        except Exception as e:
            print(f"❌ Migration error: {e}")
            return False

if __name__ == "__main__":
    success = migrate_database()
    if success:
        print("\n🎉 Database migration completed successfully!")
        print("You can now edit selling price and capital price in the product edit form.")
    else:
        print("\n❌ Database migration failed!")
