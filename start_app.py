#!/usr/bin/env python3
"""
Universal app starter that works on any device
"""

import os
import sys
import platform

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = {
        'flask': 'flask',
        'flask_sqlalchemy': 'flask-sqlalchemy',
        'pandas': 'pandas'
    }
    missing_packages = []

    for import_name, package_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("💡 Please install them with:")
        print(f"   pip install {' '.join(missing_packages)}")
        print("💡 Or install all requirements:")
        print("   pip install -r requirements.txt")
        return False

    return True

def start_app():
    """Start the Flask application with proper initialization"""
    print("🏪 ATE MEG's FROZEN FOODS POS System")
    print(f"🖥️  Platform: {platform.system()}")
    print(f"🐍 Python: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    print("=" * 50)

    # Check dependencies first
    print("🔍 Checking dependencies...")
    if not check_dependencies():
        input("Press Enter to exit...")
        return
    print("✅ All dependencies found")

    # Check if database exists, if not initialize it
    db_path = os.path.join('instance', 'frozen_foods_pos.db')
    if not os.path.exists(db_path):
        print("🔄 Database not found. Initializing...")
        try:
            import subprocess
            subprocess.run([sys.executable, "init_data.py"], check=True)
            print("✅ Database initialized successfully!")
        except Exception as e:
            print(f"❌ Error initializing database: {e}")
            return
    
    # Get local IP address
    import socket
    try:
        # Connect to a remote address to get local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()

        print("🌐 Access URLs:")
        print(f"   Local:  http://localhost:5000")
        print(f"   Network: http://{local_ip}:5000")
        print(f"   📱 Use the Network URL on your phone!")
        print(f"   POS:    http://localhost:5000/pos")
    except:
        print("🌐 Access URL: http://localhost:5000")

    print("=" * 50)
    
    # Import and run the app
    try:
        from app import app
        app.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"❌ Error starting app: {e}")
        print("💡 Try running: python init_data.py first")

if __name__ == "__main__":
    start_app()