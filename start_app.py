#!/usr/bin/env python3
"""
Universal app starter that works on any device
"""

import os
import sys
import platform
import subprocess
import socket

VENV_DIR = "venv"  # or you can name it '.venv'

def create_virtual_env():
    """Create a virtual environment if it doesn't exist"""
    print(f"⚙️  Creating virtual environment in ./{VENV_DIR}...")
    try:
        subprocess.run([sys.executable, "-m", "venv", VENV_DIR], check=True)
        print("✅ Virtual environment created.")

        pip_path = os.path.join(VENV_DIR, "Scripts" if os.name == "nt" else "bin", "pip")
        subprocess.run([pip_path, "install", "--upgrade", "pip"], check=True)

        print("📦 Installing requirements...")
        if os.path.exists("requirements.txt"):
            subprocess.run([pip_path, "install", "-r", "requirements.txt"], check=True)
        else:
            subprocess.run([pip_path, "install", "flask", "flask-sqlalchemy", "pandas"], check=True)

        print("✅ Dependencies installed inside virtual environment.")

    except Exception as e:
        print(f"❌ Failed to set up virtual environment: {e}")
        sys.exit(1)

def is_inside_venv():
    return (
        hasattr(sys, 'real_prefix') or
        (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    )

def activate_and_rerun():
    """Re-run the script using the virtual environment's python"""
    python_path = os.path.join(VENV_DIR, "Scripts" if os.name == "nt" else "bin", "python")
    print("🚀 Re-running script inside virtual environment...")
    subprocess.run([python_path, __file__])
    sys.exit(0)

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = {
        'flask': 'flask',
        'flask_sqlalchemy': 'flask-sqlalchemy',
        'pandas': 'pandas'
    }
    missing_packages = []

    for import_name, package_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("💡 Please install them with:")
        print(f"   pip install {' '.join(missing_packages)}")
        print("💡 Or install all requirements:")
        print("   pip install -r requirements.txt")
        return False

    return True

def start_app():
    """Start the Flask application with proper initialization"""
    print("🏪 ATE MEG's FROZEN FOODS POS System")
    print(f"🖥️  Platform: {platform.system()}")
    print(f"🐍 Python: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    print(f"📁 Python Path: {sys.executable}")
    print("=" * 50)

    # Setup virtual environment if not in one
    if not is_inside_venv():
        if not os.path.isdir(VENV_DIR):
            create_virtual_env()
        activate_and_rerun()

    # Check dependencies first
    print("🔍 Checking dependencies...")
    if not check_dependencies():
        print("\n💡 To fix missing dependencies:")
        print("   Option 1: pip install flask flask-sqlalchemy pandas")
        print("   Option 2: pip install -r requirements.txt")
        input("Press Enter to exit...")
        return
    print("✅ All dependencies found")

    # Check if database exists
    db_path = os.path.join('instance', 'frozen_foods_pos.db')
    if not os.path.exists(db_path):
        print("🔄 Database not found. Initializing...")
        try:
            subprocess.run([sys.executable, "init_data.py"], check=True)
            print("✅ Database initialized successfully!")
        except Exception as e:
            print(f"❌ Error initializing database: {e}")
            return

    # Show access URLs
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()

        print("🌐 Access URLs:")
        print(f"   Local:   http://localhost:5000")
        print(f"   Network: http://{local_ip}:5000")
        print(f"   POS:     http://localhost:5000/pos")
        print(f"   📱 Use the Network URL on your phone!")
    except:
        print("🌐 Access URL: http://localhost:5000")

    print("=" * 50)

    try:
        from app import app
        app.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"❌ Error starting app: {e}")
        print("💡 Try running: python init_data.py first")

if __name__ == "__main__":
    start_app()
