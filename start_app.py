#!/usr/bin/env python3
"""
Simple app starter without database recreation
"""

import os
import sys

def start_app():
    """Start the Flask application with proper initialization"""
    print("🏪 Starting ATE MEG's FROZEN FOODS POS System...")
    
    # Check if database exists, if not initialize it
    db_path = os.path.join('instance', 'frozen_foods_pos.db')
    if not os.path.exists(db_path):
        print("🔄 Database not found. Initializing...")
        try:
            import subprocess
            subprocess.run([sys.executable, "init_data.py"], check=True)
            print("✅ Database initialized successfully!")
        except Exception as e:
            print(f"❌ Error initializing database: {e}")
            return
    
    # Get local IP address
    import socket
    try:
        # Connect to a remote address to get local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()

        print("🌐 Access URLs:")
        print(f"   Local:  http://localhost:5000")
        print(f"   Network: http://{local_ip}:5000")
        print(f"   📱 Use the Network URL on your phone!")
        print(f"   POS:    http://localhost:5000/pos")
    except:
        print("🌐 Access URL: http://localhost:5000")

    print("=" * 50)
    
    # Import and run the app
    try:
        from app import app
        app.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"❌ Error starting app: {e}")
        print("💡 Try running: python init_data.py first")

if __name__ == "__main__":
    start_app()

