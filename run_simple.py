#!/usr/bin/env python3
"""
Simple startup script for ATE MEG's FROZEN FOODS POS System
This script runs the Flask app directly without virtual environment setup
"""

import os
import sys
import subprocess

def install_dependencies():
    """Install required dependencies"""
    try:
        print("📦 Installing dependencies...")
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True)
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def check_database():
    """Check if database exists and has data"""
    db_path = os.path.join('instance', 'frozen_foods_pos.db')
    
    if not os.path.exists(db_path):
        print("🔄 Database not found. Initializing...")
        return False
    
    # Check if database has products
    try:
        from app import app, Product, Transaction
        with app.app_context():
            product_count = Product.query.count()
            transaction_count = Transaction.query.count()
            
            if product_count == 0:
                print("🔄 Database empty. Adding sample data...")
                return False
            else:
                print(f"✅ Database ready with {product_count} products and {transaction_count} transactions")
                return True
    except Exception as e:
        print(f"🔄 Database issue detected: {e}")
        return False

def initialize_database():
    """Initialize database with sample data"""
    try:
        print("🗄️ Initializing database...")
        subprocess.run([sys.executable, "init_data.py"], check=True)
        
        # Check if we already have transactions before creating sample sales
        try:
            from app import app, Transaction
            with app.app_context():
                transaction_count = Transaction.query.count()
                if transaction_count == 0:
                    print("📊 Adding sample sales data...")
                    subprocess.run([sys.executable, "create_sample_sales.py"], check=True)
                else:
                    print(f"📊 Sample sales data already exists ({transaction_count} transactions)")
        except Exception as e:
            print(f"⚠️ Could not check existing transactions: {e}")
        
        print("✅ Database initialization complete!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error during initialization: {e}")
        return False

def start_app():
    """Start the Flask application"""
    print("🚀 Starting ATE MEG's FROZEN FOODS POS System...")
    print("📱 The app will open automatically in your browser")
    print("🌐 Access URL: http://localhost:5000")
    print("=" * 50)
    
    # Import and run the app
    from app import app
    app.run(host='0.0.0.0', port=5000, debug=True)

if __name__ == "__main__":
    print("🏪 ATE MEG's FROZEN FOODS POS System")
    print("=" * 50)
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies. Exiting.")
        sys.exit(1)
    
    # Check if database is ready
    if not check_database():
        if not initialize_database():
            print("❌ Failed to initialize database. Exiting.")
            sys.exit(1)
    
    # Start the application
    start_app()
