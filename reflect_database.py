#!/usr/bin/env python3
"""
Reflect and display all database content
"""

from app import app, db, Product, Sale, Transaction, TransactionItem, Customer, CreditTransaction, Cashier
from datetime import datetime

def reflect_database():
    """Display all database content in a structured way"""
    with app.app_context():
        print("🗄️  DATABASE REFLECTION - FROZEN FOODS POS")
        print("=" * 80)
        
        # 1. CASHIERS
        print("\n👥 CASHIERS")
        print("-" * 40)
        cashiers = Cashier.query.all()
        if cashiers:
            for cashier in cashiers:
                status = "✅ Active" if cashier.is_active else "❌ Inactive"
                print(f"ID: {cashier.id} | Name: {cashier.name} | Status: {status}")
                print(f"   Created: {cashier.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print("No cashiers found")
        
        # 2. PRODUCTS
        print(f"\n📦 PRODUCTS ({Product.query.count()} total)")
        print("-" * 40)
        products = Product.query.order_by(Product.name).all()
        total_stock_value = 0
        total_revenue = 0
        
        for product in products:
            stock_value = product.stock * product.price
            product_revenue = product.sold * product.price
            total_stock_value += stock_value
            total_revenue += product_revenue
            
            print(f"ID: {product.id} | {product.name}")
            print(f"   Price: ₱{product.price:.2f} | Capital Ratio: {product.capital_ratio:.2%}")
            print(f"   Stock: {product.stock} | Received: {product.received} | Sold: {product.sold}")
            print(f"   Stock Value: ₱{stock_value:.2f} | Revenue: ₱{product_revenue:.2f}")
            print(f"   Profit per unit: ₱{product.profit_per_unit:.2f} | Total profit: ₱{product.total_profit:.2f}")
            print()
        
        print(f"📊 PRODUCT SUMMARY:")
        print(f"   Total Stock Value: ₱{total_stock_value:.2f}")
        print(f"   Total Revenue: ₱{total_revenue:.2f}")
        
        # 3. CUSTOMERS
        print(f"\n👤 CUSTOMERS ({Customer.query.count()} total)")
        print("-" * 40)
        customers = Customer.query.order_by(Customer.name).all()
        total_credit = 0
        total_paid = 0
        
        for customer in customers:
            total_credit += customer.total_credit
            total_paid += customer.total_paid
            balance = customer.balance
            
            print(f"ID: {customer.id} | {customer.name}")
            if customer.phone:
                print(f"   Phone: {customer.phone}")
            if customer.address:
                print(f"   Address: {customer.address}")
            print(f"   Total Credit: ₱{customer.total_credit:.2f}")
            print(f"   Total Paid: ₱{customer.total_paid:.2f}")
            print(f"   Balance: ₱{balance:.2f}")
            print()
        
        print(f"💳 CREDIT SUMMARY:")
        print(f"   Total Credit Given: ₱{total_credit:.2f}")
        print(f"   Total Payments Received: ₱{total_paid:.2f}")
        print(f"   Outstanding Balance: ₱{total_credit - total_paid:.2f}")
        
        # 4. TRANSACTIONS
        print(f"\n💰 TRANSACTIONS ({Transaction.query.count()} total)")
        print("-" * 40)
        transactions = Transaction.query.order_by(Transaction.transaction_date.desc()).limit(20).all()
        transaction_total = 0
        
        for transaction in transactions:
            transaction_total += transaction.total_amount
            cashier_name = transaction.cashier.name if transaction.cashier else "No Cashier"
            
            print(f"ID: {transaction.id} | #{transaction.transaction_number}")
            print(f"   Amount: ₱{transaction.total_amount:.2f}")
            print(f"   Cashier: {cashier_name}")
            print(f"   Date: {transaction.transaction_date.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Show transaction items
            items = TransactionItem.query.filter_by(transaction_id=transaction.id).all()
            if items:
                print(f"   Items:")
                for item in items:
                    product_name = item.product.name if item.product else f"Product ID {item.product_id}"
                    print(f"     - {item.quantity}x {product_name} @ ₱{item.unit_price:.2f} = ₱{item.total_price:.2f}")
            print()
        
        if Transaction.query.count() > 20:
            print(f"... showing latest 20 of {Transaction.query.count()} transactions")
        
        # Transaction totals
        all_transactions_total = db.session.query(db.func.sum(Transaction.total_amount)).scalar() or 0
        print(f"💰 TRANSACTION SUMMARY:")
        print(f"   Total Transaction Value: ₱{all_transactions_total:.2f}")
        
        # 5. SALES RECORDS
        print(f"\n📈 SALES RECORDS ({Sale.query.count()} total)")
        print("-" * 40)
        sales_total = db.session.query(db.func.sum(Sale.total_price)).scalar() or 0
        recent_sales = Sale.query.order_by(Sale.sale_date.desc()).limit(10).all()
        
        for sale in recent_sales:
            product_name = sale.product.name if sale.product else f"Product ID {sale.product_id}"
            transaction_num = sale.transaction.transaction_number if sale.transaction else "No Transaction"
            
            print(f"ID: {sale.id} | {product_name}")
            print(f"   Quantity: {sale.quantity} | Unit Price: ₱{sale.unit_price:.2f}")
            print(f"   Total: ₱{sale.total_price:.2f} | Transaction: #{transaction_num}")
            print(f"   Date: {sale.sale_date.strftime('%Y-%m-%d %H:%M:%S')}")
            print()
        
        if Sale.query.count() > 10:
            print(f"... showing latest 10 of {Sale.query.count()} sales")
        
        print(f"📊 SALES SUMMARY:")
        print(f"   Total Sales Value: ₱{sales_total:.2f}")
        
        # 6. CREDIT TRANSACTIONS
        credit_transactions = CreditTransaction.query.count()
        if credit_transactions > 0:
            print(f"\n💳 CREDIT TRANSACTIONS ({credit_transactions} total)")
            print("-" * 40)
            recent_credits = CreditTransaction.query.order_by(CreditTransaction.created_at.desc()).limit(10).all()
            
            for credit in recent_credits:
                customer_name = credit.customer.name if credit.customer else f"Customer ID {credit.customer_id}"
                transaction_num = credit.transaction.transaction_number if credit.transaction else "No Transaction"
                status = "✅ Paid" if credit.is_paid else "⏳ Pending"
                
                print(f"ID: {credit.id} | {customer_name}")
                print(f"   Type: {credit.transaction_type.title()} | Amount: ₱{credit.amount:.2f}")
                print(f"   Status: {status} | Transaction: #{transaction_num}")
                print(f"   Description: {credit.description}")
                print(f"   Date: {credit.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
                print()
        
        # 7. DATABASE INTEGRITY CHECK
        print(f"\n🔍 DATABASE INTEGRITY CHECK")
        print("-" * 40)
        
        # Check for orphaned records
        orphaned_sales = Sale.query.filter(~Sale.product_id.in_(db.session.query(Product.id))).count()
        orphaned_transaction_items = TransactionItem.query.filter(~TransactionItem.product_id.in_(db.session.query(Product.id))).count()
        transactions_without_cashier = Transaction.query.filter_by(cashier_id=None).count()
        
        print(f"Orphaned Sales (no product): {orphaned_sales}")
        print(f"Orphaned Transaction Items (no product): {orphaned_transaction_items}")
        print(f"Transactions without cashier: {transactions_without_cashier}")
        
        # Revenue consistency check
        sale_revenue = db.session.query(db.func.sum(Sale.total_price)).scalar() or 0
        transaction_revenue = db.session.query(db.func.sum(Transaction.total_amount)).scalar() or 0
        transaction_item_revenue = db.session.query(db.func.sum(TransactionItem.total_price)).scalar() or 0
        
        print(f"\nRevenue Consistency:")
        print(f"   Sales Table Total: ₱{sale_revenue:.2f}")
        print(f"   Transaction Table Total: ₱{transaction_revenue:.2f}")
        print(f"   Transaction Items Total: ₱{transaction_item_revenue:.2f}")
        
        if abs(sale_revenue - transaction_revenue) < 0.01:
            print("   ✅ Sales and Transactions match")
        else:
            print(f"   ⚠️  Discrepancy: ₱{abs(sale_revenue - transaction_revenue):.2f}")
        
        print("\n" + "=" * 80)
        print("🎉 Database reflection complete!")

if __name__ == "__main__":
    reflect_database()
