# 🍖 ATE MEG's FROZEN FOODS - Advanced POS System

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-2.0+-green.svg)](https://flask.palletsprojects.com)
[![SQLite](https://img.shields.io/badge/Database-SQLite-lightgrey.svg)](https://sqlite.org)
[![Bootstrap](https://img.shields.io/badge/Frontend-Bootstrap_5-purple.svg)](https://getbootstrap.com)

🚀 **INSTANT RUN**: [![Open in GitHub Codespaces](https://github.com/codespaces/badge.svg)](https://codespaces.new/laganzonj/FROZEN_FOODS)

A comprehensive, modern Point of Sale (POS) system built with Flask for managing frozen food inventory, sales, customer credit transactions, and business analytics. Designed specifically for small to medium-sized frozen food businesses with advanced financial tracking and real-time reporting capabilities.

## 🌟 Key Features Overview

### 📊 **Real-Time Financial Dashboard**
- **Live Revenue Tracking**: Separate cash sales and credit collections
- **Today's Performance**: Real-time sales metrics with historical comparisons
- **Top Sellers Today**: Dynamic product performance with revenue breakdown
- **Financial Summary**: Total revenue, profit, outstanding debt, and transactions
- **Stock Alerts**: Low stock and out-of-stock notifications with counts

### 🛒 **Advanced Point of Sale System**
- **Dual Payment Processing**: Cash and credit transaction handling
- **Customer Credit Management**: Integrated customer database with debt tracking
- **Reseller Discounts**: Automatic wholesale pricing for registered customers
- **Real-Time Stock Updates**: Instant inventory adjustments with validation
- **Alphabetical Product Sorting**: Easy navigation through product catalog
- **Shopping Cart**: Interactive cart with quantity controls and pricing

### 💳 **Comprehensive Credit Management**
- **Credit Sales Processing**: Allow customers to purchase on credit
- **Payment Collection**: Record and track customer debt payments
- **Outstanding Balance Tracking**: Real-time debt monitoring per customer
- **Credit Transaction History**: Complete audit trail of all credit activities
- **Revenue Recognition**: Proper accounting - credit becomes revenue when paid

### 📦 **Advanced Inventory Management**
- **Product Catalog**: Comprehensive database with pricing and stock levels
- **Capital Price Tracking**: Profit margin calculations with cost management
- **Stock Movement Analysis**: Detailed inventory flow tracking
- **Automatic Stock Updates**: Real-time adjustments from sales transactions
- **Low Stock Alerts**: Configurable notifications for reordering

### 👥 **Cashier Performance Analytics**
- **Individual Metrics**: Sales, profit, and transaction tracking per cashier
- **Performance Rankings**: Comparative analysis and leaderboards
- **Revenue Attribution**: Separate tracking of cash sales vs credit collections
- **Transaction Analysis**: Average transaction values and customer patterns
- **Profit Margin Tracking**: Individual cashier profitability analysis

### 📈 **Advanced Reporting & Analytics**
- **Sales Reports**: Detailed analysis with date filtering and product breakdown
- **Financial Reports**: Revenue, profit, and debt analysis with trends
- **Product Performance**: Best and worst-performing products with metrics
- **Customer Analysis**: Credit customer behavior and payment patterns
- **Export Capabilities**: CSV export for external analysis and accounting

### 🔒 **Data Management & Security**
- **Automatic CSV Backups**: Real-time backups of all transactions and changes
- **Data Integrity**: Comprehensive validation and error handling
- **Complete Audit Trail**: Full transaction logging with timestamps
- **Database Migration**: Automated schema updates and data preservation

## 🚀 Quick Start Guide

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)

### Installation & Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/laganzonj/FROZEN_FOODS.git
   cd FROZEN_FOODS
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Initialize the database**
   ```bash
   python init_data.py
   ```

4. **Run the application**
   ```bash
   python app.py
   ```

5. **Access the system**
   Open your browser and navigate to `http://localhost:5000`

## 💼 Business Workflow

### Daily Operations

1. **Morning Setup**
   - Check dashboard for overnight activity
   - Review low stock alerts
   - Check outstanding customer debts

2. **Sales Processing**
   - Use POS system for customer transactions
   - Process cash sales for immediate revenue
   - Handle credit sales for regular customers
   - Collect payments from customers with outstanding debt

3. **Inventory Management**
   - Monitor stock levels throughout the day
   - Update product prices as needed
   - Add new products to catalog

4. **End of Day**
   - Review daily sales performance
   - Check cashier performance metrics
   - Export reports for accounting

### Financial Cycle Understanding

**Revenue Recognition Model:**
- **Cash Sales** → Immediate Revenue ✅
- **Credit Sales** → Creates Customer Debt (No Revenue Yet) ⏳
- **Customer Payments** → Converts Debt to Revenue ✅
- **Total Revenue** = Cash Sales + Customer Payments Received

## 🏗️ Technical Architecture

### Database Schema

**Core Tables:**
- **Products**: Inventory catalog with pricing and stock
- **Transactions**: All sales transactions with payment methods
- **TransactionItems**: Individual items within transactions
- **Sales**: Product-specific sales records
- **Customers**: Customer database with credit tracking
- **CreditTransactions**: Credit sales and payment history
- **Cashiers**: Staff management and performance tracking

### Key Components

**Backend (Flask):**
- `app.py`: Main application with all routes and business logic
- `init_data.py`: Database initialization and sample data
- `backup_system.py`: Automated CSV backup functionality
- `test_financial_logic.py`: Financial calculation validation

**Frontend (Bootstrap 5 + JavaScript):**
- Responsive design for desktop and mobile
- Real-time updates and interactive components
- Modern UI with professional styling

**Data Management:**
- SQLite database for development
- Automatic CSV backups in `backups/` folder
- Real-time data synchronization

## 📱 User Interface Features

### Dashboard
- **Financial KPIs**: Revenue, profit, debt, transactions
- **Performance Metrics**: Today's sales vs historical data
- **Quick Actions**: Direct access to key functions
- **Visual Indicators**: Color-coded alerts and status

### Point of Sale
- **Product Grid**: Visual product catalog with stock levels
- **Shopping Cart**: Real-time cart management
- **Customer Selection**: Quick customer lookup for credit sales
- **Payment Processing**: Cash and credit transaction handling

### Reports
- **Sales Analytics**: Product performance and revenue trends
- **Inventory Reports**: Stock levels and movement analysis
- **Customer Reports**: Credit customer management
- **Cashier Performance**: Individual staff metrics

## 🔧 Configuration & Customization

### Product Management
- Add/edit/delete products through web interface
- Set capital prices for profit calculation
- Configure stock levels and reorder points
- Manage product categories and pricing

### Customer Management
- Add customers for credit sales
- Track payment history and outstanding balances
- Set credit limits and payment terms
- Generate customer statements

### System Settings
- Configure reseller discount percentages
- Set low stock alert thresholds
- Customize backup schedules
- Manage cashier accounts

## 📊 Sample Data Included

**Pre-loaded Products:**
- Chicken Products: Nuggets, Teriyaki, Pastil, Tocino
- Pork Products: Sisig, Dinakdakan, Teriyaki, Tapa, Tocino
- Specialty Items: Longanisa varieties, Siomai, Hamonado

**Sample Transactions:**
- Historical sales data for testing
- Multiple cashier transactions
- Mix of cash and credit sales
- Customer payment records

## 🛠️ Development & Deployment

### Local Development
```bash
# Run in development mode
python app.py

# Run tests
python test_financial_logic.py

# Import sample transactions
python import_transactions.py
```

### Production Deployment
- **Heroku**: Ready for deployment with Procfile
- **Railway/Render**: One-click deployment support
- **VPS**: Standard Flask deployment procedures

### Environment Variables
```bash
DATABASE_URL=sqlite:///frozen_foods_pos.db  # Development
SECRET_KEY=your-secret-key-here
FLASK_ENV=production
```

## 📈 Business Intelligence Features

### Financial Analytics
- Revenue tracking with cash vs credit breakdown
- Profit margin analysis by product and cashier
- Outstanding debt monitoring and collection tracking
- Daily, weekly, and monthly performance reports

### Inventory Intelligence
- Stock turnover analysis
- Product performance rankings
- Reorder point optimization
- Seasonal trend analysis

### Customer Insights
- Credit customer behavior analysis
- Payment pattern tracking
- Customer lifetime value calculation
- Risk assessment for credit limits

## 🆘 Troubleshooting

### Common Issues

**Database Connection Error:**
```bash
python init_data.py  # Reinitialize database
```

**Port Already in Use:**
```bash
# Change port in app.py
app.run(debug=True, port=5001)
```

**Missing Dependencies:**
```bash
pip install -r requirements.txt
```

**Data Backup Issues:**
- Check `backups/` folder permissions
- Verify CSV export functionality
- Review backup logs in console

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support, questions, or feature requests:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the documentation

---

**🎉 Happy Selling!**

*Built with ❤️ for frozen food businesses*

**© 2025 ATE MEG's FROZEN FOODS™ | Professional POS System**
