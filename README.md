# 🏪 ATE MEG's FROZEN FOODS - Professional POS System

🚀 **INSTANT RUN**: [![Open in GitHub Codespaces](https://github.com/codespaces/badge.svg)](https://codespaces.new/laganzonj/FROZEN_FOODS)

🌐 **One-Click Deploy**: [![Deploy to Render](https://render.com/images/deploy-to-render-button.svg)](https://render.com/deploy?repo=https://github.com/laganzonj/FROZEN_FOODS)

A comprehensive Point of Sale (POS) system built with Flask for managing frozen food inventory, sales, and reporting. Perfect for small to medium-sized frozen food businesses.

## ✨ Features

### 📦 Product Management

- ✅ Add, edit, and delete products
- ✅ Track inventory levels (received, sold, current stock)
- ✅ Price management
- ✅ Low stock alerts
- ✅ Automatic stock calculations

### 🛒 Point of Sale (POS)

- ✅ Interactive shopping cart
- ✅ Real-time stock checking
- ✅ Transaction processing
- ✅ Automatic inventory updates
- ✅ Receipt generation

### 📊 Reports & Analytics

- ✅ Sales reports with product performance
- ✅ Inventory reports with stock levels
- ✅ Transaction history
- ✅ Daily sales trends
- ✅ Low stock alerts
- ✅ Revenue analytics

### 🎯 Dashboard

- ✅ Quick overview of business metrics
- ✅ Low stock alerts
- ✅ Quick action buttons
- ✅ Real-time statistics

## 🚀 Quick Start

### Prerequisites

- Python 3.10 or higher
- pip (Python package installer)

### Local Development

1. **Clone the repository**

   ```bash
   git clone https://github.com/laganzonj/FROZEN_FOODS.git
   cd FROZEN_FOODS
   ```

2. **Install dependencies**

   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**

   ```bash
   python app.py
   ```

   _Note: The database will be automatically initialized with sample data on first run_

4. **Open your browser**
   Navigate to `http://localhost:5000`

### 🌐 Production Deployment

#### GitHub Deployment

```bash
# Initialize git repository
git init
git add .
git commit -m "Initial commit - ATE MEG's FROZEN FOODS POS System"

# Connect to GitHub
git branch -M main
git remote add origin https://github.com/laganzonj/FROZEN_FOODS.git
git push -u origin main
```

#### Heroku Deployment

```bash
# Install Heroku CLI, then:
heroku create your-app-name
git push heroku main
heroku open
```

#### Railway/Render Deployment

1. Connect your GitHub repository
2. Set build command: `pip install -r requirements.txt`
3. Set start command: `python app.py`
4. Deploy automatically

## 📱 Usage Guide

### Getting Started

1. **Dashboard**: View your business overview and quick stats
2. **Products**: Manage your frozen food inventory
3. **POS**: Process sales and transactions
4. **Reports**: Analyze your business performance

### Managing Products

1. Go to **Products** section
2. Click **Add New Product** to add items
3. Fill in product details:
   - Product name
   - Price per unit
   - Quantity received
4. Edit or delete products as needed

### Processing Sales

1. Go to **POS** section
2. Click products to add them to cart
3. Adjust quantities as needed
4. Click **Process Sale** to complete transaction
5. View transaction confirmation

### Viewing Reports

1. Go to **Reports** section
2. Choose from:
   - **Sales Report**: Revenue and product performance
   - **Inventory Report**: Stock levels and alerts
   - **Transaction History**: Complete transaction log

## 🏗️ Project Structure

```
FROZEN_FOODS/
├── app.py                 # Main Flask application
├── init_data.py          # Database initialization script
├── requirements.txt      # Python dependencies
├── README.md            # This file
├── templates/           # HTML templates
│   ├── base.html        # Base template
│   ├── index.html       # Dashboard
│   ├── products.html    # Product management
│   ├── add_product.html # Add product form
│   ├── edit_product.html# Edit product form
│   ├── pos.html         # Point of sale interface
│   ├── reports.html     # Reports overview
│   ├── sales_report.html# Sales analytics
│   ├── inventory_report.html # Inventory status
│   └── transaction_history.html # Transaction log
└── frozen_foods_pos.db  # SQLite database (created automatically)
```

## 🗄️ Database Schema

### Products Table

- `id`: Primary key
- `name`: Product name
- `price`: Selling price per unit
- `stock`: Current stock level
- `received`: Total quantity received
- `sold`: Total quantity sold
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

### Sales Table

- `id`: Primary key
- `product_id`: Foreign key to products
- `quantity`: Quantity sold
- `unit_price`: Price per unit at time of sale
- `total_price`: Total amount for this sale item
- `sale_date`: Sale timestamp

### Transactions Table

- `id`: Primary key
- `transaction_number`: Unique transaction identifier
- `total_amount`: Total transaction amount
- `transaction_date`: Transaction timestamp

### Transaction Items Table

- `id`: Primary key
- `transaction_id`: Foreign key to transactions
- `product_id`: Foreign key to products
- `quantity`: Quantity in this transaction
- `unit_price`: Price per unit
- `total_price`: Total for this item

## 🛠️ Customization

### Adding New Products

The system comes pre-loaded with your frozen food inventory:

- Beef Burger Patty (₱80)
- Big Jumbo Siomai (₱70)
- Cheesy Hambutid (₱90)
- Chicken Nuggets (₱60)
- Chicken Pastil (₱110)
- Chicken Teriyaki (₱80)
- Chicken Tocino (₱80)
- Pork Dinakdakan (₱110)
- Pork Longadog (₱65)
- Pork Longanisa (₱65)
- Pork Meatballs (₱60)
- Pork Sisig (₱110)
- Pork Tapa (₱75)
- Pork Teriyaki (₱75)
- Pork Tocino (₱75)
- Salami w/ Cheese (₱90)
- Skinless Longanisa (₱65)
- Sliced Ham (₱75)

### Modifying Prices

1. Go to Products section
2. Click edit button for any product
3. Update price and save

### 💾 Data Persistence & Backup

#### Local Development

- Data is stored in `instance/frozen_foods_pos.db` (SQLite)
- Automatically created on first run with sample data
- Back up this file regularly to prevent data loss

#### Production Deployment

- **Heroku**: Use Heroku Postgres add-on for persistent data
- **Railway/Render**: Automatic database persistence included
- **Custom hosting**: Ensure database files are in persistent storage

#### Database Migration

```bash
# To preserve existing data when deploying:
# 1. Export current data (if needed)
# 2. Deploy application
# 3. Data will be automatically preserved in production
```

## 🔧 Technical Details

### Built With

- **Flask**: Web framework
- **SQLAlchemy**: Database ORM
- **SQLite**: Database
- **Bootstrap 5**: Frontend framework
- **Font Awesome**: Icons
- **JavaScript**: Interactive features

### Browser Compatibility

- Chrome (recommended)
- Firefox
- Safari
- Edge

## 📈 Business Benefits

### Inventory Management

- ✅ Real-time stock tracking
- ✅ Automatic low stock alerts
- ✅ Prevent overselling
- ✅ Track product performance

### Sales Efficiency

- ✅ Fast transaction processing
- ✅ Accurate calculations
- ✅ Professional receipts
- ✅ Error reduction

### Business Intelligence

- ✅ Sales analytics
- ✅ Product performance insights
- ✅ Revenue tracking
- ✅ Inventory optimization

## 🆘 Troubleshooting

### Common Issues

**Database not found**

```bash
python init_data.py
```

**Port already in use**

```bash
# Change port in app.py
app.run(debug=True, port=5001)
```

**Missing dependencies**

```bash
pip install -r requirements.txt
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 📞 Support

For support or questions:

- Create an issue on GitHub
- Check the troubleshooting section
- Review the usage guide

---

**Happy Selling! 🎉**

_Built with ❤️ for frozen food businesses_

**© 2025 jLagzn STUDIO | ATE MEG's FROZEN FOODS™**
