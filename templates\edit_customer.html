{% extends "base.html" %}

{% block title %}Edit Customer - ATE MEG's FROZEN FOODS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-user-edit"></i> Edit Customer
            </h1>
            <a href="{{ url_for('customers') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Customers
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Customer Information</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label">Customer Name *</label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="{{ customer.name }}" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="{{ customer.phone or '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="address" class="form-label">Address</label>
                                <textarea class="form-control" id="address" name="address" rows="3">{{ customer.address or '' }}</textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('customers') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Customer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line"></i> Customer Statistics
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h5 class="text-danger">₱{{ "%.2f"|format(customer.total_credit) }}</h5>
                        <small class="text-muted">Total Credit</small>
                    </div>
                    <div class="col-4">
                        <h5 class="text-success">₱{{ "%.2f"|format(customer.total_paid) }}</h5>
                        <small class="text-muted">Total Paid</small>
                    </div>
                    <div class="col-4">
                        <h5 class="{% if customer.balance > 0 %}text-warning{% else %}text-info{% endif %}">
                            ₱{{ "%.2f"|format(customer.balance|abs) }}
                        </h5>
                        <small class="text-muted">
                            {% if customer.balance > 0 %}Outstanding{% elif customer.balance < 0 %}Overpaid{% else %}Balanced{% endif %}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tools"></i> Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('customer_credit', customer_id=customer.id) }}" class="btn btn-info">
                        <i class="fas fa-credit-card"></i> View Credit History
                    </a>
                    {% if customer.balance > 0 %}
                    <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#paymentModal">
                        <i class="fas fa-money-bill"></i> Record Payment
                    </button>
                    {% endif %}
                    <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#creditModal">
                        <i class="fas fa-plus"></i> Add Credit
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Record Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('add_payment', customer_id=customer.id) }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="payment_amount" class="form-label">Payment Amount (₱)</label>
                        <input type="number" class="form-control" id="payment_amount" name="amount" 
                               step="0.01" min="0" max="{{ customer.balance }}" required>
                        <div class="form-text">Outstanding balance: ₱{{ "%.2f"|format(customer.balance) }}</div>
                    </div>
                    <div class="mb-3">
                        <label for="payment_description" class="form-label">Description</label>
                        <input type="text" class="form-control" id="payment_description" name="description" 
                               value="Payment received" placeholder="Payment description">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Record Payment</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Credit Modal -->
<div class="modal fade" id="creditModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Credit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('add_credit', customer_id=customer.id) }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="credit_amount" class="form-label">Credit Amount (₱)</label>
                        <input type="number" class="form-control" id="credit_amount" name="amount" 
                               step="0.01" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label for="credit_description" class="form-label">Description</label>
                        <input type="text" class="form-control" id="credit_description" name="description" 
                               placeholder="Reason for credit (e.g., Purchase on credit)">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Credit</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
