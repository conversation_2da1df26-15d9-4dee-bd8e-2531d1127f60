#!/usr/bin/env python3
"""
Simulate cashier performance by creating sample transactions for different cashiers
"""

from app import app, db, Product, Transaction, TransactionItem, Sale, Cashier
from datetime import datetime, timezone, timedelta
import random

def simulate_cashier_performance():
    """Create sample transactions for different cashiers to demonstrate performance tracking"""
    with app.app_context():
        print("🎭 Simulating Cashier Performance Data...")
        print("=" * 60)
        
        # Get all cashiers
        cashiers = Cashier.query.all()
        products = Product.query.all()
        
        if not cashiers:
            print("❌ No cashiers found. Please run init_data.py first.")
            return
        
        if not products:
            print("❌ No products found. Please run init_data.py first.")
            return
        
        print(f"👥 Found {len(cashiers)} cashiers:")
        for cashier in cashiers:
            print(f"   • {cashier.name}")
        
        print(f"📦 Found {len(products)} products available")
        
        # Simulate different performance levels for each cashier
        cashier_profiles = {
            'Abbie': {'transactions': 15, 'avg_items': 3, 'performance': 'high'},
            'Nonoy': {'transactions': 12, 'avg_items': 2, 'performance': 'medium'},
            'Gian': {'transactions': 18, 'avg_items': 4, 'performance': 'high'},
            'Caleb': {'transactions': 8, 'avg_items': 2, 'performance': 'low'},
            'Yasha': {'transactions': 14, 'avg_items': 3, 'performance': 'medium'},
            'House': {'transactions': 10, 'avg_items': 2, 'performance': 'medium'}
        }
        
        transaction_counter = 1000  # Start from 1000 to avoid conflicts
        
        for cashier in cashiers:
            profile = cashier_profiles.get(cashier.name, {'transactions': 5, 'avg_items': 2, 'performance': 'low'})
            
            print(f"\n💰 Creating transactions for {cashier.name}...")
            print(f"   Target: {profile['transactions']} transactions, avg {profile['avg_items']} items each")
            
            for i in range(profile['transactions']):
                # Generate transaction number
                transaction_counter += 1
                transaction_number = f"SIM{transaction_counter:08d}"
                
                # Random date within last 30 days
                days_ago = random.randint(0, 30)
                transaction_date = datetime.now(timezone.utc) - timedelta(days=days_ago)
                
                # Create transaction
                transaction = Transaction(
                    transaction_number=transaction_number,
                    total_amount=0,  # Will be calculated
                    cashier_id=cashier.id,
                    transaction_date=transaction_date
                )
                db.session.add(transaction)
                db.session.flush()  # Get transaction ID
                
                # Add random items to transaction
                num_items = random.randint(1, profile['avg_items'] * 2)
                total_amount = 0
                
                for _ in range(num_items):
                    product = random.choice(products)
                    quantity = random.randint(1, 3)
                    
                    # Create transaction item
                    item = TransactionItem(
                        transaction_id=transaction.id,
                        product_id=product.id,
                        quantity=quantity,
                        unit_price=product.price,
                        total_price=product.price * quantity
                    )
                    db.session.add(item)
                    
                    # Create sale record
                    sale = Sale(
                        product_id=product.id,
                        transaction_id=transaction.id,
                        quantity=quantity,
                        unit_price=product.price,
                        total_price=product.price * quantity,
                        sale_date=transaction_date
                    )
                    db.session.add(sale)
                    
                    # Update product sold count
                    product.sold += quantity
                    
                    total_amount += product.price * quantity
                
                # Update transaction total
                transaction.total_amount = total_amount
                
                if (i + 1) % 5 == 0:
                    print(f"   ✅ Created {i + 1}/{profile['transactions']} transactions")
            
            print(f"   🎉 Completed {profile['transactions']} transactions for {cashier.name}")
        
        # Commit all changes
        db.session.commit()
        
        print("\n" + "=" * 60)
        print("🎉 Simulation Complete!")
        print("\n📊 Performance Summary:")
        
        # Show summary
        for cashier in cashiers:
            transactions = Transaction.query.filter_by(cashier_id=cashier.id).all()
            total_sales = sum(t.total_amount for t in transactions)
            total_transactions = len(transactions)
            
            print(f"   {cashier.name}: {total_transactions} transactions, ₱{total_sales:.2f} sales")
        
        print("\n💡 Now visit the Cashier Performance page to see the results!")
        print("   URL: http://localhost:5000/cashier_performance")

if __name__ == "__main__":
    simulate_cashier_performance()
