{% extends "base.html" %} {% block title %}Edit Product - Frozen Foods POS{%
endblock %} {% block content %}

<style>
  /* Base Card Styling */
  .card {
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .card:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  }

  .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    font-weight: 600;
  }

  /* Form Controls */
  .form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
  }

  .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
  }

  .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
  }

  /* <PERSON><PERSON>yling */
  .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
  }

  .btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    border: none;
  }

  .btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268 0%, #3d4449 100%);
    transform: translateY(-2px);
  }

  /* Specialized Cards */
  .inventory-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 1.5rem;
  }

  .stock-adjustment-card {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #ffeaa7;
    border-radius: 12px;
    padding: 1.5rem;
  }

  /* Statistics Display */
  .stat-number {
    font-size: 2.2rem;
    font-weight: 700;
    line-height: 1;
  }

  .stat-label {
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  /* Input Groups */
  .input-group-text {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-color: #ced4da;
    font-weight: 600;
    border-radius: 8px 0 0 8px;
  }

  .input-group .form-control {
    border-radius: 0 8px 8px 0;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .card {
      margin-bottom: 1rem;
    }

    .stat-number {
      font-size: 1.8rem;
    }

    .inventory-card,
    .stock-adjustment-card {
      padding: 1rem;
    }
  }
</style>

<div class="row">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h1><i class="fas fa-edit"></i> Edit Product</h1>
      <a href="{{ url_for('products') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Products
      </a>
    </div>
  </div>
</div>

<div class="row justify-content-center">
  <div class="col-md-8">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">Product Information</h5>
      </div>
      <div class="card-body">
        <form method="POST">
          <div class="mb-3">
            <label for="name" class="form-label">Product Name *</label>
            <input
              type="text"
              class="form-control"
              id="name"
              name="name"
              value="{{ product.name }}"
              required
            />
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="price" class="form-label"
                  >Selling Price (₱) *</label
                >
                <input
                  type="number"
                  class="form-control"
                  id="price"
                  name="price"
                  step="0.01"
                  min="0"
                  value="{{ product.price }}"
                  required
                />
                <div class="form-text">Price customers pay</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="capital_price" class="form-label"
                  >Capital Price (₱) *</label
                >
                <input
                  type="number"
                  class="form-control"
                  id="capital_price"
                  name="capital_price"
                  step="0.01"
                  min="0"
                  value="{{ product.capital_price }}"
                  required
                />
                <div class="form-text">Your cost/purchase price</div>
              </div>
            </div>
          </div>

          <!-- Current Stock Information -->
          <div class="row mb-4">
            <div class="col-md-3">
              <div class="card text-center">
                <div class="card-body">
                  <h3 class="text-primary">{{ product.stock }}</h3>
                  <small class="text-muted">Current Stock</small>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card text-center">
                <div class="card-body">
                  <h3 class="text-info">{{ product.received }}</h3>
                  <small class="text-muted">Net Received</small>
                  <div class="small text-info">All adjustments</div>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card text-center">
                <div class="card-body">
                  <h3 class="text-success">{{ product.sold }}</h3>
                  <small class="text-muted">Total Sold</small>
                  <div class="small text-success">-{{ product.sold }} sold</div>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card text-center">
                <div class="card-body">
                  <h3 class="text-warning">
                    ₱{{ "%.2f"|format(product.price) }}
                  </h3>
                  <small class="text-muted">Current Price</small>
                  <div class="small text-muted">Per unit</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Stock Flow Explanation -->
          <div class="row mb-4">
            <div class="col-12">
              <div class="alert alert-light border">
                <div class="text-center small">
                  <strong>Stock Balance:</strong>
                  <span class="text-info"
                    >{{ product.received }} Net Received</span
                  >
                  - <span class="text-success">{{ product.sold }} Sold</span> =
                  <span class="text-primary"
                    ><strong>{{ product.stock }} Current Stock</strong></span
                  >
                  {% if product.received - product.sold - product.stock != 0 %}
                  <span class="text-danger"
                    >({{ product.received - product.sold - product.stock }}
                    discrepancy)</span
                  >
                  {% endif %}
                </div>
              </div>
            </div>
          </div>

          <!-- Stock Adjustment Section -->
          <div class="row mb-4">
            <div class="col-12">
              <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                  <h6 class="mb-0">
                    <i class="fas fa-edit"></i> Stock Adjustment
                  </h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="mb-3">
                        <label for="stock_adjustment" class="form-label">
                          <i class="fas fa-plus-minus"></i> Add/Remove Stock
                        </label>
                        <input
                          type="number"
                          class="form-control"
                          id="stock_adjustment"
                          name="stock_adjustment"
                          value="0"
                          placeholder="Enter positive to add, negative to remove"
                        />
                        <div class="form-text">
                          <strong>Examples:</strong><br />
                          • Enter <code>+10</code> to add 10 units (new
                          delivery)<br />
                          • Enter <code>-5</code> to remove 5 units
                          (damaged/expired)<br />
                          • Enter <code>0</code> for no change
                        </div>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="mb-3">
                        <label for="adjustment_reason" class="form-label">
                          <i class="fas fa-comment"></i> Reason for Adjustment
                        </label>
                        <select
                          class="form-control"
                          id="adjustment_reason"
                          name="adjustment_reason"
                        >
                          <option value="">Select reason...</option>
                          <option value="new_delivery">
                            New Delivery/Restock
                          </option>
                          <option value="damaged_expired">
                            Damaged/Expired Items
                          </option>
                          <option value="inventory_correction">
                            Inventory Count Correction
                          </option>
                          <option value="wrong_input">
                            Wrong Input/Data Entry Error
                          </option>
                          <option value="theft_loss">Theft/Loss</option>
                          <option value="other">Other</option>
                        </select>
                        <div class="form-text">
                          This helps track why stock was adjusted
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="alert alert-info">
                    <strong>New Stock:</strong>
                    <span id="new_stock_preview">{{ product.stock }}</span>
                    units
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Profit Information -->
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label">Profit per Unit</label>
                <input
                  type="text"
                  class="form-control"
                  id="profit_display"
                  readonly
                />
                <div class="form-text">Selling price - Capital price</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label">Total Profit Potential</label>
                <input
                  type="text"
                  class="form-control"
                  id="total_profit_display"
                  readonly
                />
                <div class="form-text">Profit per unit × Current stock</div>
              </div>
            </div>
          </div>

          <div class="d-grid gap-2 d-md-flex justify-content-md-end">
            <a
              href="{{ url_for('products') }}"
              class="btn btn-secondary me-md-2"
            >
              <i class="fas fa-times"></i> Cancel
            </a>
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save"></i> Update Product
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Help and Quick Actions Row -->
<div class="row">
  <div class="col-md-6">
    <div class="card border-info">
      <div class="card-header bg-info text-white">
        <h6 class="mb-0">
          <i class="fas fa-lightbulb"></i> How to Use Stock Adjustment
        </h6>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <h6 class="text-success">
            <i class="fas fa-plus"></i> Adding Stock (+)
          </h6>
          <ul class="small mb-2">
            <li><strong>+20:</strong> New delivery of 20 units</li>
            <li><strong>+3:</strong> Found 3 missing items</li>
          </ul>
        </div>
        <div class="mb-3">
          <h6 class="text-warning">
            <i class="fas fa-minus"></i> Removing Stock (-)
          </h6>
          <ul class="small mb-2">
            <li><strong>-2:</strong> 2 items damaged</li>
            <li><strong>-5:</strong> 5 items expired</li>
            <li><strong>-3:</strong> Wrong input correction</li>
          </ul>
        </div>
        <div class="alert alert-warning small mb-0">
          <i class="fas fa-exclamation-triangle"></i>
          <strong>Important:</strong> Changes are permanent!
        </div>
      </div>
    </div>
  </div>

  <div class="col-md-6">
    <div class="card border-success">
      <div class="card-header bg-success text-white">
        <h6 class="mb-0"><i class="fas fa-chart-line"></i> Quick Actions</h6>
      </div>
      <div class="card-body">
        <a
          href="{{ url_for('product_history', id=product.id) }}"
          class="btn btn-info btn-sm mb-2 w-100"
        >
          <i class="fas fa-history"></i> View Transaction History
        </a>
        <a
          href="{{ url_for('products') }}"
          class="btn btn-secondary btn-sm w-100"
        >
          <i class="fas fa-arrow-left"></i> Back to Products
        </a>
        <hr class="my-3" />
        <div class="small text-muted">
          <strong>Current Status:</strong><br />
          • Stock: {{ product.stock }} units<br />
          • Price: ₱{{ "%.2f"|format(product.price) }}<br />
          • Total Sold: {{ product.sold }} units
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block scripts %}
<script>
  document.addEventListener("DOMContentLoaded", function () {
    const currentStock = {{ product.stock }};

    // Calculate profit in real-time
    function calculateProfit() {
      const sellingPrice =
        parseFloat(document.getElementById("price").value) || 0;
      const capitalPrice =
        parseFloat(document.getElementById("capital_price").value) || 0;
      const profit = sellingPrice - capitalPrice;

      // Get stock adjustment properly
      const adjustmentInput = document.getElementById("stock_adjustment");
      const adjustmentValue = adjustmentInput.value.trim();

      let stockAdjustment = 0;
      if (adjustmentValue !== '' && adjustmentValue !== '0') {
        if (adjustmentValue.startsWith('-')) {
          stockAdjustment = -Math.abs(parseFloat(adjustmentValue.substring(1)) || 0);
        } else if (adjustmentValue.startsWith('+')) {
          stockAdjustment = Math.abs(parseFloat(adjustmentValue.substring(1)) || 0);
        } else {
          stockAdjustment = parseFloat(adjustmentValue) || 0;
        }
      }

      const newStock = currentStock + stockAdjustment;

      document.getElementById("profit_display").value = "₱" + profit.toFixed(2);
      document.getElementById("total_profit_display").value = "₱" + (profit * Math.max(0, newStock)).toFixed(2);

      // Update display color based on profit
      const profitField = document.getElementById("profit_display");
      if (profit > 0) {
        profitField.className = "form-control text-success fw-bold";
      } else if (profit < 0) {
        profitField.className = "form-control text-danger fw-bold";
      } else {
        profitField.className = "form-control text-muted";
      }
    }

    // Update stock preview in real-time
    function updateStockPreview() {
      const adjustmentInput = document.getElementById("stock_adjustment");
      const adjustmentValue = adjustmentInput.value.trim();

      // Handle empty input
      if (adjustmentValue === '' || adjustmentValue === '0') {
        const previewElement = document.getElementById("new_stock_preview");
        previewElement.textContent = currentStock;
        previewElement.className = "text-primary";
        calculateProfit();
        return;
      }

      // Parse the adjustment value (handles negative numbers properly)
      let stockAdjustment = 0;
      if (adjustmentValue.startsWith('-')) {
        stockAdjustment = -Math.abs(parseFloat(adjustmentValue.substring(1)) || 0);
      } else if (adjustmentValue.startsWith('+')) {
        stockAdjustment = Math.abs(parseFloat(adjustmentValue.substring(1)) || 0);
      } else {
        stockAdjustment = parseFloat(adjustmentValue) || 0;
      }

      const newStock = currentStock + stockAdjustment;
      const previewElement = document.getElementById("new_stock_preview");

      previewElement.textContent = newStock;

      // Color coding for stock preview
      if (stockAdjustment > 0) {
        previewElement.className = "text-success fw-bold";
      } else if (stockAdjustment < 0) {
        previewElement.className = "text-warning fw-bold";
      } else {
        previewElement.className = "text-primary";
      }

      // Warning for negative stock
      if (newStock < 0) {
        previewElement.className = "text-danger fw-bold";
      }

      // Update total profit potential
      calculateProfit();
    }

    // Add event listeners for real-time calculation
    document.getElementById("price").addEventListener("input", calculateProfit);
    document.getElementById("capital_price").addEventListener("input", calculateProfit);
    document.getElementById("stock_adjustment").addEventListener("input", updateStockPreview);

    // Format price inputs on blur
    const priceInput = document.getElementById("price");
    const capitalPriceInput = document.getElementById("capital_price");

    priceInput.addEventListener("blur", function () {
      if (this.value) {
        this.value = parseFloat(this.value).toFixed(2);
      }
      calculateProfit();
    });

    capitalPriceInput.addEventListener("blur", function () {
      if (this.value) {
        this.value = parseFloat(this.value).toFixed(2);
      }
      calculateProfit();
    });

    // Validate stock adjustment to prevent negative stock
    document.getElementById("stock_adjustment").addEventListener("blur", function () {
      const adjustment = parseFloat(this.value) || 0;
      const newStock = currentStock + adjustment;

      if (newStock < 0) {
        alert("Warning: This adjustment would result in negative stock (" + newStock + "). Please check your input.");
        this.focus();
      }
    });

    // Calculate initial values
    calculateProfit();
    updateStockPreview();
  });
</script>
{% endblock %}
