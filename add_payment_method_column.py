#!/usr/bin/env python3
"""
Database migration to add payment_method column to Transaction table
"""

import sqlite3
import os
from datetime import datetime

def add_payment_method_column():
    """Add payment_method column to Transaction table"""
    
    db_path = 'instance/frozen_foods_pos.db'
    
    print("🔄 Adding payment_method column to Transaction table...")
    print("=" * 60)
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if column already exists
        cursor.execute('PRAGMA table_info("transaction")')
        columns = [column[1] for column in cursor.fetchall()]

        if 'payment_method' in columns:
            print("✅ payment_method column already exists")
        else:
            print("➕ Adding payment_method column...")

            # Add the column with default value 'cash'
            cursor.execute("""
                ALTER TABLE "transaction"
                ADD COLUMN payment_method TEXT DEFAULT 'cash'
            """)

            print("✅ payment_method column added successfully")

        # Update existing transactions to have payment_method = 'cash'
        print("🔄 Setting default payment_method for existing transactions...")

        cursor.execute("""
            UPDATE "transaction"
            SET payment_method = 'cash'
            WHERE payment_method IS NULL
        """)
        
        updated_rows = cursor.rowcount
        print(f"✅ Updated {updated_rows} transactions to 'cash' payment method")
        
        # Check for credit transactions and update them
        print("🔄 Checking for credit transactions...")
        
        cursor.execute("""
            SELECT DISTINCT transaction_id 
            FROM credit_transaction 
            WHERE transaction_id IS NOT NULL
        """)
        
        credit_transaction_ids = [row[0] for row in cursor.fetchall()]
        
        if credit_transaction_ids:
            print(f"💳 Found {len(credit_transaction_ids)} credit transactions")
            
            # Update credit transactions to have payment_method = 'credit'
            placeholders = ','.join(['?' for _ in credit_transaction_ids])
            cursor.execute(f"""
                UPDATE "transaction"
                SET payment_method = 'credit'
                WHERE id IN ({placeholders})
            """, credit_transaction_ids)
            
            credit_updated = cursor.rowcount
            print(f"✅ Updated {credit_updated} transactions to 'credit' payment method")
        else:
            print("ℹ️  No credit transactions found")
        
        # Commit changes
        conn.commit()
        
        # Show final summary
        print("\n📊 FINAL SUMMARY:")
        cursor.execute('SELECT payment_method, COUNT(*) FROM "transaction" GROUP BY payment_method')
        results = cursor.fetchall()

        for payment_method, count in results:
            print(f"   {payment_method}: {count} transactions")

        cursor.execute('SELECT COUNT(*) FROM "transaction"')
        total = cursor.fetchone()[0]
        print(f"   Total: {total} transactions")
        
        conn.close()
        
        print("\n🎉 Migration completed successfully!")
        print("💡 You can now restart the Flask app")
        
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        raise

if __name__ == '__main__':
    add_payment_method_column()
