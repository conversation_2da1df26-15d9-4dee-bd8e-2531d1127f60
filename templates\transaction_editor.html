<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Transaction Date Editor - ATE MEG's FROZEN FOODS</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <link
      href="{{ url_for('static', filename='css/style.css') }}"
      rel="stylesheet"
    />
    <style>
      .transaction-card {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
      }
      .transaction-card:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-color: #007bff;
      }
      .transaction-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px;
        border-radius: 8px 8px 0 0;
      }
      .transaction-body {
        padding: 15px;
      }
      .date-editor {
        background-color: #f8f9fa;
        border-radius: 6px;
        padding: 15px;
        margin-top: 10px;
      }
      .item-list {
        max-height: 200px;
        overflow-y: auto;
      }
      .item-badge {
        background-color: #e9ecef;
        color: #495057;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.85em;
        margin: 2px;
        display: inline-block;
      }
      .btn-edit-date {
        background: linear-gradient(135deg, #28a745, #20c997);
        border: none;
        color: white;
      }
      .btn-edit-date:hover {
        background: linear-gradient(135deg, #218838, #1ea080);
        color: white;
      }
      .loading-spinner {
        display: none;
      }
      .search-box {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    </style>
  </head>
  <body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
      <div class="container">
        <a class="navbar-brand" href="{{ url_for('index') }}">
          <img
            src="{{ url_for('static', filename='trademark/blurbgicon.png') }}"
            alt="Logo"
            width="30"
            height="30"
            class="d-inline-block align-text-top me-2"
          />
          ATE MEG's FROZEN FOODS
        </a>
        <div class="navbar-nav ms-auto">
          <a class="nav-link" href="{{ url_for('index') }}">
            <i class="fas fa-home"></i> Home
          </a>
          <a class="nav-link" href="{{ url_for('reports') }}">
            <i class="fas fa-chart-bar"></i> Reports
          </a>
        </div>
      </div>
    </nav>

    <div class="container mt-4">
      <div class="row">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
              <i class="fas fa-edit text-primary"></i> Transaction Date Editor
            </h2>
            <div>
              <button
                class="btn btn-outline-primary"
                onclick="loadTransactions()"
              >
                <i class="fas fa-sync-alt"></i> Refresh
              </button>
            </div>
          </div>

          <!-- Search and Filter -->
          <div class="search-box">
            <div class="row">
              <div class="col-md-6">
                <label class="form-label">Search Transactions</label>
                <input
                  type="text"
                  class="form-control"
                  id="searchInput"
                  placeholder="Search by transaction number, cashier, or amount..."
                />
              </div>
              <div class="col-md-3">
                <label class="form-label">Filter by Cashier</label>
                <select class="form-select" id="cashierFilter">
                  <option value="">All Cashiers</option>
                </select>
              </div>
              <div class="col-md-3">
                <label class="form-label">Filter by Date</label>
                <input type="date" class="form-control" id="dateFilter" />
              </div>
            </div>
          </div>

          <!-- Loading Spinner -->
          <div class="text-center loading-spinner" id="loadingSpinner">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading transactions...</p>
          </div>

          <!-- Transactions List -->
          <div id="transactionsList">
            <!-- Transactions will be loaded here -->
          </div>
        </div>
      </div>
    </div>

    <!-- Edit Date Modal -->
    <div class="modal fade" id="editDateModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-calendar-alt text-primary"></i> Edit Transaction
              Date
            </h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label class="form-label">Transaction Number</label>
              <input
                type="text"
                class="form-control"
                id="modalTransactionNumber"
                readonly
              />
            </div>
            <div class="mb-3">
              <label class="form-label">Current Date</label>
              <input
                type="text"
                class="form-control"
                id="modalCurrentDate"
                readonly
              />
            </div>
            <div class="mb-3">
              <label class="form-label">New Date</label>
              <input
                type="date"
                class="form-control"
                id="modalNewDate"
                required
              />
            </div>
            <div class="mb-3">
              <label class="form-label">Cashier</label>
              <input
                type="text"
                class="form-control"
                id="modalCashier"
                readonly
              />
            </div>
            <div class="mb-3">
              <label class="form-label">Total Amount</label>
              <input
                type="text"
                class="form-control"
                id="modalTotalAmount"
                readonly
              />
            </div>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button
              type="button"
              class="btn btn-primary"
              onclick="updateTransactionDate()"
            >
              <i class="fas fa-save"></i> Update Date
            </button>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      let allTransactions = [];
      let currentTransactionId = null;

      // Load transactions on page load
      document.addEventListener("DOMContentLoaded", function () {
        loadTransactions();

        // Add search functionality
        document
          .getElementById("searchInput")
          .addEventListener("input", filterTransactions);
        document
          .getElementById("cashierFilter")
          .addEventListener("change", filterTransactions);
        document
          .getElementById("dateFilter")
          .addEventListener("change", filterTransactions);
      });

      function loadTransactions() {
        document.getElementById("loadingSpinner").style.display = "block";
        document.getElementById("transactionsList").innerHTML = "";

        fetch("/api/transactions")
          .then((response) => response.json())
          .then((data) => {
            document.getElementById("loadingSpinner").style.display = "none";

            if (data.success) {
              allTransactions = data.transactions;
              populateCashierFilter();
              displayTransactions(allTransactions);
            } else {
              showAlert("Error loading transactions: " + data.error, "danger");
            }
          })
          .catch((error) => {
            document.getElementById("loadingSpinner").style.display = "none";
            showAlert("Error loading transactions: " + error.message, "danger");
          });
      }

      function populateCashierFilter() {
        const cashierFilter = document.getElementById("cashierFilter");
        const cashiers = [
          ...new Set(allTransactions.map((t) => t.cashier_name)),
        ].sort();

        cashierFilter.innerHTML = '<option value="">All Cashiers</option>';
        cashiers.forEach((cashier) => {
          cashierFilter.innerHTML += `<option value="${cashier}">${cashier}</option>`;
        });
      }

      function displayTransactions(transactions) {
        const container = document.getElementById("transactionsList");

        if (transactions.length === 0) {
          container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No transactions found</h5>
                        <p class="text-muted">Try adjusting your search criteria</p>
                    </div>
                `;
          return;
        }

        container.innerHTML = transactions
          .map(
            (transaction) => `
                <div class="transaction-card">
                    <div class="transaction-header">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h6 class="mb-1">
                                    <i class="fas fa-receipt"></i> ${
                                      transaction.transaction_number
                                    }
                                </h6>
                                <small>
                                    <i class="fas fa-user"></i> ${
                                      transaction.cashier_name
                                    } | 
                                    <i class="fas fa-peso-sign"></i> ₱${parseFloat(
                                      transaction.total_amount
                                    ).toFixed(2)} | 
                                    <i class="fas fa-box"></i> ${
                                      transaction.item_count
                                    } items
                                </small>
                            </div>
                            <div class="col-md-4 text-end">
                                <button class="btn btn-edit-date btn-sm" onclick="openEditModal(${
                                  transaction.id
                                })">
                                    <i class="fas fa-edit"></i> Edit Date
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="transaction-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Current Date:</strong><br>
                                <span class="text-primary">
                                    <i class="fas fa-calendar"></i> ${formatDateTime(
                                      transaction.transaction_date
                                    )}
                                </span>
                            </div>
                            <div class="col-md-6">
                                <strong>Items:</strong><br>
                                <div class="item-list">
                                    ${transaction.items
                                      .map(
                                        (item) => `
                                        <span class="item-badge">
                                            ${item.quantity}x ${
                                          item.product_name
                                        } @ ₱${parseFloat(
                                          item.unit_price
                                        ).toFixed(2)}
                                        </span>
                                    `
                                      )
                                      .join("")}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `
          )
          .join("");
      }

      function filterTransactions() {
        const searchTerm = document
          .getElementById("searchInput")
          .value.toLowerCase();
        const cashierFilter = document.getElementById("cashierFilter").value;
        const dateFilter = document.getElementById("dateFilter").value;

        let filtered = allTransactions.filter((transaction) => {
          const matchesSearch =
            !searchTerm ||
            transaction.transaction_number.toLowerCase().includes(searchTerm) ||
            transaction.cashier_name.toLowerCase().includes(searchTerm) ||
            transaction.total_amount.toString().includes(searchTerm);

          const matchesCashier =
            !cashierFilter || transaction.cashier_name === cashierFilter;

          const matchesDate =
            !dateFilter || transaction.transaction_date.startsWith(dateFilter);

          return matchesSearch && matchesCashier && matchesDate;
        });

        displayTransactions(filtered);
      }

      function openEditModal(transactionId) {
        const transaction = allTransactions.find((t) => t.id === transactionId);
        if (!transaction) return;

        currentTransactionId = transactionId;

        document.getElementById("modalTransactionNumber").value =
          transaction.transaction_number;
        document.getElementById("modalCurrentDate").value = formatDateTime(
          transaction.transaction_date
        );
        document.getElementById("modalCashier").value =
          transaction.cashier_name;
        document.getElementById("modalTotalAmount").value =
          "₱" + parseFloat(transaction.total_amount).toFixed(2);

        // Set new date to current date for editing (date only)
        const currentDate = new Date(transaction.transaction_date_iso);
        document.getElementById("modalNewDate").value = currentDate
          .toISOString()
          .slice(0, 10);

        new bootstrap.Modal(document.getElementById("editDateModal")).show();
      }

      function updateTransactionDate() {
        const newDate = document.getElementById("modalNewDate").value;

        if (!newDate) {
          showAlert("Please select a new date", "warning");
          return;
        }

        const updateData = {
          new_date: newDate, // Send date string directly (YYYY-MM-DD format)
        };

        fetch(`/api/transactions/${currentTransactionId}/update_date`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updateData),
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              showAlert(
                `Transaction date updated successfully!<br>
                              Transaction: ${data.transaction_number}<br>
                              Old Date: ${data.old_date}<br>
                              New Date: ${data.new_date}`,
                "success"
              );

              // Close modal and reload transactions
              bootstrap.Modal.getInstance(
                document.getElementById("editDateModal")
              ).hide();
              loadTransactions();
            } else {
              showAlert(
                "Error updating transaction date: " + data.error,
                "danger"
              );
            }
          })
          .catch((error) => {
            showAlert(
              "Error updating transaction date: " + error.message,
              "danger"
            );
          });
      }

      function formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString("en-US", {
          year: "numeric",
          month: "short",
          day: "numeric",
        });
      }

      function showAlert(message, type) {
        const alertDiv = document.createElement("div");
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

        document
          .querySelector(".container")
          .insertBefore(
            alertDiv,
            document.querySelector(".container").firstChild
          );

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
          if (alertDiv.parentNode) {
            alertDiv.remove();
          }
        }, 5000);
      }
    </script>
  </body>
</html>
