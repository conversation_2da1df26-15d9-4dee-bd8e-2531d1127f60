#!/usr/bin/env python3
"""
Test the live backup system by performing various database operations
"""

import requests
import json
import time

def test_live_backup_operations():
    """Test various database operations to see live backup in action"""
    print("🧪 Testing Live Backup System...")
    print("=" * 60)
    
    base_url = 'http://localhost:5000'
    
    # Test 1: Add a new cashier
    print("\n1️⃣ Testing: Add New Cashier")
    print("   Operation: POST /api/cashiers")
    try:
        response = requests.post(
            f'{base_url}/api/cashiers',
            headers={'Content-Type': 'application/json'},
            data=json.dumps({'name': 'Test Cashier Live Backup'})
        )
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ Cashier added: {data['cashier']['name']}")
                print("   📁 CSV backup should be triggered automatically!")
            else:
                print(f"   ❌ Failed: {data.get('error')}")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
    
    time.sleep(2)  # Wait for backup to complete
    
    # Test 2: Trigger full backup
    print("\n2️⃣ Testing: Full Database Backup")
    print("   Operation: GET /api/backup/full")
    try:
        response = requests.get(f'{base_url}/api/backup/full')
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"   ✅ Full backup completed!")
                print(f"   📂 Backup directory: {data.get('backup_directory')}")
            else:
                print(f"   ❌ Failed: {data.get('error')}")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🔍 BACKUP BEHAVIOR EXPLANATION:")
    print("=" * 60)
    
    print("\n📋 OPERATIONS THAT TRIGGER AUTOMATIC CSV BACKUP:")
    print("   ✅ Add new product → CSV updated")
    print("   ✅ Edit product (price, stock, etc.) → CSV updated")
    print("   ✅ Add new customer → CSV updated")
    print("   ✅ Edit customer info → CSV updated")
    print("   ✅ Add new cashier → CSV updated")
    print("   ✅ Process new transaction → CSV updated")
    print("   ✅ Update transaction cashier → CSV updated")
    print("   ✅ Add customer payment → CSV updated")
    
    print("\n📁 CSV FILES CREATED:")
    print("   📄 backups/live_updates/products_current.csv")
    print("   📄 backups/live_updates/customers_current.csv")
    print("   📄 backups/live_updates/cashiers_current.csv")
    print("   📄 backups/live_updates/transactions_current.csv")
    print("   📄 backups/live_updates/[table]_live_update_[timestamp].csv")
    
    print("\n🔄 REAL-TIME SYNCHRONIZATION:")
    print("   • Database changes → Immediate CSV backup")
    print("   • CSV files always reflect current database state")
    print("   • Timestamped backup files for audit trail")
    print("   • Current state files for latest data")
    
    print("\n💡 HOW TO VERIFY:")
    print("   1. Make changes in the POS system")
    print("   2. Check backups/live_updates/ folder")
    print("   3. Open CSV files to see updated data")
    print("   4. Compare with database content")
    
    print("\n🎯 BACKUP TYPES:")
    print("   📦 FULL BACKUP: Complete database snapshot")
    print("      • Triggered: Manual backup button or API call")
    print("      • Location: backups/backup_YYYYMMDD_HHMMSS/")
    print("      • Contains: All tables with complete history")
    
    print("\n   🔄 LIVE BACKUP: Real-time updates")
    print("      • Triggered: Any database change")
    print("      • Location: backups/live_updates/")
    print("      • Contains: Current state + timestamped changes")
    
    print("\n   📅 DAILY BACKUP: Transaction logs")
    print("      • Triggered: Each transaction")
    print("      • Location: backups/transactions/transactions_YYYYMMDD.csv")
    print("      • Contains: Daily transaction details")

if __name__ == "__main__":
    test_live_backup_operations()
