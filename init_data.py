
#!/usr/bin/env python3
"""
Initialize ATE MEG's FROZEN FOODS database with complete product and customer data
Includes selling price, capital price, and proper profit calculations
"""

from app import app, db, Product, Customer, Sale, Transaction, TransactionItem, CreditTransaction, Cashier, Settings, ProductTransaction
from datetime import datetime, timezone, timedelta
import random

def create_sample_product_transactions():
    """Create sample product transaction history for demonstration"""

    # Sample transaction data for some products
    sample_transactions = [
        # Chicken Pastil - Initial delivery and some adjustments
        {"product_name": "Chicken Pastil", "type": "stock_add", "quantity": 25, "reason": "new_delivery", "days_ago": 7, "notes": "Initial delivery from supplier"},
        {"product_name": "Chicken Pastil", "type": "stock_remove", "quantity": -3, "reason": "damaged_items", "days_ago": 5, "notes": "3 units damaged during transport"},
        {"product_name": "Chicken Pastil", "type": "price_change", "old_price": 105.00, "new_price": 110.00, "old_capital": 70.00, "new_capital": 75.00, "days_ago": 4, "notes": "Price adjustment due to supplier cost increase"},

        # Big Siomai - Delivery and inventory correction
        {"product_name": "Big Siomai", "type": "stock_add", "quantity": 20, "reason": "new_delivery", "days_ago": 6, "notes": "Weekly delivery"},
        {"product_name": "Big Siomai", "type": "stock_add", "quantity": 5, "reason": "inventory_correction", "days_ago": 3, "notes": "Found 5 additional units during inventory count"},
        {"product_name": "Big Siomai", "type": "stock_remove", "quantity": -3, "reason": "expired_items", "days_ago": 2, "notes": "3 units expired, removed from inventory"},

        # Beef Burger Patty - Multiple adjustments
        {"product_name": "Beef Burger Patty", "type": "stock_add", "quantity": 25, "reason": "new_delivery", "days_ago": 8, "notes": "Bulk delivery for the week"},
        {"product_name": "Beef Burger Patty", "type": "stock_remove", "quantity": -2, "reason": "damaged_items", "days_ago": 6, "notes": "2 units damaged in freezer malfunction"},
        {"product_name": "Beef Burger Patty", "type": "stock_remove", "quantity": -2, "reason": "inventory_correction", "days_ago": 1, "notes": "Inventory count correction - 2 units missing"},

        # Chicken Nuggets - Price change and stock adjustments
        {"product_name": "Chicken Nuggets", "type": "stock_add", "quantity": 25, "reason": "new_delivery", "days_ago": 9, "notes": "Monthly bulk order"},
        {"product_name": "Chicken Nuggets", "type": "stock_remove", "quantity": -5, "reason": "expired_items", "days_ago": 4, "notes": "5 units past expiration date"},
        {"product_name": "Chicken Nuggets", "type": "price_change", "old_price": 55.00, "new_price": 60.00, "old_capital": 35.00, "new_capital": 40.00, "days_ago": 3, "notes": "Price increase due to market conditions"},

        # Pork Sisig - Recent adjustments
        {"product_name": "Pork Sisig", "type": "stock_add", "quantity": 25, "reason": "new_delivery", "days_ago": 5, "notes": "Fresh delivery from supplier"},
        {"product_name": "Pork Sisig", "type": "stock_remove", "quantity": -3, "reason": "damaged_items", "days_ago": 2, "notes": "3 units damaged during handling"},
    ]

    print(f"   Creating {len(sample_transactions)} sample transactions...")

    for trans_data in sample_transactions:
        # Find the product
        product = Product.query.filter_by(name=trans_data["product_name"]).first()
        if not product:
            continue

        # Calculate the transaction date
        transaction_date = datetime.now(timezone.utc) - timedelta(days=trans_data["days_ago"])

        if trans_data["type"] in ["stock_add", "stock_remove"]:
            # Create stock transaction
            transaction = ProductTransaction(
                product_id=product.id,
                transaction_type=trans_data["type"],
                quantity_change=trans_data["quantity"],
                reason=trans_data["reason"],
                notes=trans_data["notes"],
                created_at=transaction_date
            )
            db.session.add(transaction)
            print(f"     ✅ {product.name}: {trans_data['type']} ({trans_data['quantity']:+d} units) - {trans_data['reason']}")

        elif trans_data["type"] == "price_change":
            # Create price change transaction
            transaction = ProductTransaction(
                product_id=product.id,
                transaction_type="price_change",
                old_price=trans_data["old_price"],
                new_price=trans_data["new_price"],
                old_capital_price=trans_data["old_capital"],
                new_capital_price=trans_data["new_capital"],
                notes=trans_data["notes"],
                created_at=transaction_date
            )
            db.session.add(transaction)
            print(f"     ✅ {product.name}: price_change (₱{trans_data['old_price']:.2f} → ₱{trans_data['new_price']:.2f})")

    db.session.commit()
    print(f"   🎉 Successfully created sample transaction history!")

def init_sample_data():
    """Initialize the database with ATE MEG's frozen foods inventory data"""

    with app.app_context():
        # Create all tables with updated schema
        db.create_all()

        # Clear existing data for fresh start
        print("🗑️ Clearing existing data...")

        # Clear all tables in proper order (foreign key dependencies)
        db.session.query(CreditTransaction).delete()
        db.session.query(TransactionItem).delete()
        db.session.query(Transaction).delete()
        db.session.query(Sale).delete()
        db.session.query(ProductTransaction).delete()
        db.session.query(Customer).delete()
        db.session.query(Product).delete()
        db.session.query(Cashier).delete()
        db.session.query(Settings).delete()

        db.session.commit()
        print("✅ All existing data cleared successfully")

        # ATE MEG's FROZEN FOODS - Complete inventory with selling and capital prices
        frozen_foods_data = [
            {"name": "Chicken Pastil", "selling_price": 110.00, "capital_price": 75.00, "received": 22, "sold": 0, "stock": 22},
            {"name": "Pork Sisig", "selling_price": 110.00, "capital_price": 75.00, "received": 22, "sold": 0, "stock": 22},
            {"name": "Cheesy Hamonado", "selling_price": 90.00, "capital_price": 60.00, "received": 21, "sold": 0, "stock": 21},
            {"name": "Pork Dinakdakan", "selling_price": 110.00, "capital_price": 75.00, "received": 22, "sold": 0, "stock": 22},
            {"name": "Chicken Teriyaki", "selling_price": 80.00, "capital_price": 55.00, "received": 16, "sold": 0, "stock": 16},
            {"name": "Chicken Tocino", "selling_price": 80.00, "capital_price": 55.00, "received": 15, "sold": 0, "stock": 15},
            {"name": "Pork Tapa", "selling_price": 75.00, "capital_price": 50.00, "received": 22, "sold": 0, "stock": 22},
            {"name": "Pork Longadog", "selling_price": 65.00, "capital_price": 45.00, "received": 21, "sold": 0, "stock": 21},
            {"name": "Pork Teriyaki", "selling_price": 75.00, "capital_price": 50.00, "received": 24, "sold": 0, "stock": 24},
            {"name": "Pork Tocino", "selling_price": 75.00, "capital_price": 50.00, "received": 20, "sold": 0, "stock": 20},
            {"name": "Skinless Longanisa", "selling_price": 65.00, "capital_price": 45.00, "received": 16, "sold": 0, "stock": 16},
            {"name": "Pork Longanisa", "selling_price": 65.00, "capital_price": 45.00, "received": 15, "sold": 0, "stock": 15},
            {"name": "Big Siomai", "selling_price": 70.00, "capital_price": 45.00, "received": 22, "sold": 0, "stock": 22},
            {"name": "Salami w/ Cheese", "selling_price": 90.00, "capital_price": 60.00, "received": 11, "sold": 0, "stock": 11},
            {"name": "Pork Meatballs", "selling_price": 60.00, "capital_price": 40.00, "received": 20, "sold": 0, "stock": 20},
            {"name": "Chicken Nuggets", "selling_price": 60.00, "capital_price": 40.00, "received": 20, "sold": 0, "stock": 20},
            {"name": "Sliced Ham", "selling_price": 75.00, "capital_price": 50.00, "received": 10, "sold": 0, "stock": 10},
            {"name": "Beef Burger Patty", "selling_price": 80.00, "capital_price": 55.00, "received": 21, "sold": 0, "stock": 21}
        ]

        print("🏪 Initializing ATE MEG's FROZEN FOODS inventory...")
        print("=" * 60)

        for item_data in frozen_foods_data:
            # Create product with selling price and capital price
            product = Product(
                name=item_data["name"],
                price=item_data["selling_price"],  # Selling price
                received=item_data["received"],
                sold=item_data["sold"],
                stock=item_data["stock"],
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )

            # Set capital price using the model's method
            product.set_capital_price(item_data["capital_price"])

            db.session.add(product)

            # Calculate profit for display
            profit_per_unit = item_data["selling_price"] - item_data["capital_price"]
            total_profit = 0  # No profit yet since nothing sold

            print(f"✅ {item_data['name']:<20} | Selling: ₱{item_data['selling_price']:<6.2f} | Capital: ₱{item_data['capital_price']:<6.2f} | Profit/Unit: ₱{profit_per_unit:<6.2f} | Stock: {item_data['stock']:<3} | Total Profit: ₱{total_profit:<7.2f}")

        db.session.commit()
        print("=" * 60)
        print(f"🎉 Successfully initialized {len(frozen_foods_data)} products!")

        # Create sample product transactions for realistic history
        print(f"\n📋 Creating sample product transaction history...")
        create_sample_product_transactions()

        # Calculate and display summary
        total_revenue = 0  # No sales yet
        total_capital_invested = sum(item["received"] * item["capital_price"] for item in frozen_foods_data)
        total_profit = 0  # No profit yet

        print(f"\n📊 BUSINESS SUMMARY:")
        print(f"   💰 Total Revenue: ₱{total_revenue:,.2f}")
        print(f"   💸 Capital Invested: ₱{total_capital_invested:,.2f}")
        print(f"   📈 Total Profit: ₱{total_profit:,.2f}")
        print(f"   📊 Profit Margin: 0.0% (No sales yet)")

        # Add sample customers for credit management
        sample_customers = [
            {"name": "Bets", "phone": "***********", "address": "sa tabi ng inomart"}
        ]

        print(f"\n👥 Adding sample customers...")
        for customer_data in sample_customers:
            customer = Customer(
                name=customer_data["name"],
                phone=customer_data["phone"],
                address=customer_data["address"],
                total_credit=0.0,  # Reset to zero (original state)
                total_paid=0.0,    # Reset to zero (original state)
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            db.session.add(customer)
            print(f"   ✅ {customer_data['name']} - {customer_data['phone']} (Balance: ₱0.00)")

        db.session.commit()
        print(f"\n🎉 Successfully initialized {len(sample_customers)} sample customers!")
        print("💰 All customers start with zero credit balance (original state)")

        # Add cashiers
        cashiers_data = [
            {"name": "Abbie"},
            {"name": "Nonoy"},
            {"name": "Gian"},
            {"name": "Caleb"},
            {"name": "Yasha"},
            {"name": "House"},
            {"name": "Mama"}
        ]

        print(f"\n👥 Adding cashiers...")
        for cashier_data in cashiers_data:
            cashier = Cashier(
                name=cashier_data["name"],
                is_active=True,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            db.session.add(cashier)
            print(f"   ✅ {cashier_data['name']} - Active")

        db.session.commit()
        print(f"\n🎉 Successfully initialized {len(cashiers_data)} cashiers!")

        # Initialize system settings
        print(f"\n⚙️ Initializing system settings...")
        default_settings = [
            {
                "key": "reseller_discount",
                "value": "5.0",
                "description": "Discount amount in pesos for reseller customers"
            }
        ]

        for setting_data in default_settings:
            setting = Settings(
                key=setting_data["key"],
                value=setting_data["value"],
                description=setting_data["description"],
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            db.session.add(setting)
            print(f"   ✅ {setting_data['key']}: {setting_data['value']} - {setting_data['description']}")

        db.session.commit()
        print(f"\n🎉 Successfully initialized {len(default_settings)} system settings!")

        print("\n" + "=" * 60)
        print("🏪 ATE MEG's FROZEN FOODS DATABASE READY!")
        print("=" * 60)
        print("📋 Features Available:")
        print("   • Product Management with Profit Calculations")
        print("   • Sales Tracking and Reports")
        print("   • Inventory Management with Stock Adjustments")
        print("   • Product Transaction History (Stock & Price Changes)")
        print("   • Customer Credit Management")
        print("   • Cashier Management and Transaction Tracking")
        print("   • Transaction History with Cashier Info")
        print("   • Financial Analytics")
        print("   • Automatic CSV Backup System")
        print("   • Sales History Management (Manual Transaction Entry)")
        print("   • Reseller Discount System")
        print("   • Configurable Settings Management")
        print("   • Date-Only Transaction Recording")

        print("\n🗄️ Database Tables Created:")
        print("   📦 Products - Inventory with selling/capital prices")
        print("   👥 Customers - Customer information and credit tracking")
        print("   🧑‍💼 Cashiers - Staff members who process transactions")
        print("   💰 Sales - Individual sale records")
        print("   📋 Transactions - Complete transaction records with cashier info")
        print("   📝 TransactionItems - Detailed transaction line items")
        print("   💳 CreditTransactions - Credit and payment tracking")
        print("   📊 ProductTransactions - Product movement and price change history")
        print("   ⚙️ Settings - System configuration and preferences")

        print("\n🔧 Important Columns Added:")
        print("   • Product.capital_ratio - For profit calculations")
        print("   • Customer.total_credit - Total credit given")
        print("   • Customer.total_paid - Total payments received")
        print("   • Transaction.cashier_id - Links transactions to cashiers")
        print("   • Transaction.customer_type - 'default' or 'reseller' pricing")
        print("   • Cashier.is_active - Active/inactive status")
        print("   • CreditTransaction.transaction_type - 'credit' or 'payment'")
        print("   • CreditTransaction.is_paid - Payment status tracking")
        print("   • ProductTransaction.transaction_type - 'stock_add', 'stock_remove', 'price_change'")
        print("   • ProductTransaction.quantity_change - Stock adjustment amounts")
        print("   • ProductTransaction.reason - Reason for stock adjustments")
        print("   • Settings.key/value - System configuration storage")

        print("\n🌐 Access your POS system at: http://localhost:5000")
        print("=" * 60)

if __name__ == "__main__":
    init_sample_data()



