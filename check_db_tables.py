#!/usr/bin/env python3
"""
Check database tables and structure
"""

import sqlite3
import os

def check_database():
    """Check database structure"""
    
    db_path = 'instance/frozen_foods_pos.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return
    
    print("🔍 Checking database structure...")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("📋 Tables found:")
        for table in tables:
            print(f"   - {table[0]}")
        
        # Check transaction table specifically
        if any('transaction' in table[0].lower() for table in tables):
            print("\n🔍 Transaction table details:")
            
            # Find the exact table name
            transaction_table = None
            for table in tables:
                if 'transaction' in table[0].lower():
                    transaction_table = table[0]
                    break
            
            if transaction_table:
                print(f"   Table name: {transaction_table}")
                cursor.execute(f'PRAGMA table_info("{transaction_table}")')
                columns = cursor.fetchall()
                
                print("   Columns:")
                for col in columns:
                    print(f"     - {col[1]} ({col[2]})")
                
                # Check if payment_method exists
                column_names = [col[1] for col in columns]
                if 'payment_method' in column_names:
                    print("   ✅ payment_method column exists")
                else:
                    print("   ❌ payment_method column missing")
        else:
            print("\n❌ No transaction table found")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        if 'conn' in locals():
            conn.close()

if __name__ == '__main__':
    check_database()
