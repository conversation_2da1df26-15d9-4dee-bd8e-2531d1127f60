{% extends "base.html" %}

{% block title %}Add Product - Frozen Foods POS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-plus"></i> Add New Product
            </h1>
            <a href="{{ url_for('products') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Products
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Product Information</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label">Product Name *</label>
                        <input type="text" class="form-control" id="name" name="name" required 
                               placeholder="Enter product name (e.g., <PERSON><PERSON>)">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">Price (₱) *</label>
                                <input type="number" class="form-control" id="price" name="price" 
                                       step="0.01" min="0" required placeholder="0.00">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="received" class="form-label">Quantity Received *</label>
                                <input type="number" class="form-control" id="received" name="received" 
                                       min="0" required placeholder="0">
                                <div class="form-text">This will be your initial stock quantity</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('products') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Add Product
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Tips for Adding Products
                </h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li><strong>Product Name:</strong> Use clear, descriptive names that match your inventory</li>
                    <li><strong>Price:</strong> Set the selling price per unit</li>
                    <li><strong>Quantity Received:</strong> Enter the number of units you received/have in stock</li>
                    <li><strong>Stock Management:</strong> The system will automatically track sales and update stock levels</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on product name field
    document.getElementById('name').focus();
    
    // Format price input
    const priceInput = document.getElementById('price');
    priceInput.addEventListener('blur', function() {
        if (this.value) {
            this.value = parseFloat(this.value).toFixed(2);
        }
    });
});
</script>
{% endblock %}
