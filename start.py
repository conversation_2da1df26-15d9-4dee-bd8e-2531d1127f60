#!/usr/bin/env python3
"""
Startup script for ATE MEG's FROZEN FOODS POS System
This script ensures the database is initialized and starts the Flask app
"""

import os
import sys
import subprocess
import venv

def setup_virtual_environment():
    """Create and activate virtual environment if it doesn't exist"""
    venv_path = os.path.join(os.getcwd(), 'venv')

    if not os.path.exists(venv_path):
        print("🔧 Creating virtual environment...")
        venv.create(venv_path, with_pip=True)
        print("✅ Virtual environment created!")

    # Get the correct python executable path for the virtual environment
    if os.name == 'nt':  # Windows
        python_exe = os.path.join(venv_path, 'Scripts', 'python.exe')
        pip_exe = os.path.join(venv_path, 'Scripts', 'pip.exe')
    else:  # Unix/Linux/Mac
        python_exe = os.path.join(venv_path, 'bin', 'python')
        pip_exe = os.path.join(venv_path, 'bin', 'pip')

    return python_exe, pip_exe

def install_dependencies(pip_exe):
    """Install required dependencies"""
    try:
        print("📦 Installing dependencies...")
        subprocess.run([pip_exe, "install", "-r", "requirements.txt"],
                      check=True, capture_output=True, text=True)
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def check_database():
    """Check if database exists and has data"""
    db_path = os.path.join('instance', 'frozen_foods_pos.db')

    if not os.path.exists(db_path):
        print("🔄 Database not found. Initializing...")
        return False

    # Check if database has products
    try:
        from app import app, Product, Transaction
        with app.app_context():
            product_count = Product.query.count()
            transaction_count = Transaction.query.count()

            if product_count == 0:
                print("🔄 Database empty. Adding sample data...")
                return False
            else:
                print(f"✅ Database ready with {product_count} products and {transaction_count} transactions")
                return True
    except Exception as e:
        print(f"🔄 Database issue detected: {e}")
        return False

def initialize_database(python_exe):
    """Initialize database with sample data"""
    try:
        print("🗄️ Initializing database...")
        subprocess.run([python_exe, "init_data.py"], check=True)

        # Check if we already have transactions before creating sample sales
        try:
            from app import app, Transaction
            with app.app_context():
                transaction_count = Transaction.query.count()
                if transaction_count == 0:
                    print("📊 Adding sample sales data...")
                    subprocess.run([python_exe, "create_sample_sales.py"], check=True)
                else:
                    print(f"📊 Sample sales data already exists ({transaction_count} transactions)")
        except Exception as e:
            print(f"⚠️ Could not check existing transactions: {e}")

        print("✅ Database initialization complete!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error during initialization: {e}")
        return False

def start_app():
    """Start the Flask application"""
    print("🚀 Starting ATE MEG's FROZEN FOODS POS System...")
    print("📱 The app will open automatically in your browser")
    print("🌐 Access URL: http://localhost:5000")
    print("=" * 50)

    # Import and run the app
    from app import app
    app.run(host='0.0.0.0', port=5000, debug=True)

if __name__ == "__main__":
    print("🏪 ATE MEG's FROZEN FOODS POS System")
    print("=" * 50)

    # Setup virtual environment and get python executable
    python_exe, pip_exe = setup_virtual_environment()

    # Install dependencies
    if not install_dependencies(pip_exe):
        print("❌ Failed to install dependencies. Exiting.")
        sys.exit(1)

    # Check if database is ready
    if not check_database():
        if not initialize_database(python_exe):
            print("❌ Failed to initialize database. Exiting.")
            sys.exit(1)

    # Start the application
    start_app()
