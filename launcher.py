#!/usr/bin/env python3
"""
Universal launcher for ATE MEG's FROZEN FOODS POS System
Works on any device with Python installed
"""

import sys
import os
import subprocess
import platform

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python 3.7 or higher is required")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        print("💡 Please upgrade Python from https://python.org")
        return False
    return True

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = {
        'flask': 'flask',
        'flask_sqlalchemy': 'flask-sqlalchemy',
        'pandas': 'pandas'
    }
    missing_packages = []

    for import_name, package_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 Install missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        print("\n💡 Or install all requirements with:")
        print("   pip install -r requirements.txt")
        return False
    
    return True

def get_system_info():
    """Get system information for troubleshooting"""
    return {
        'platform': platform.system(),
        'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        'python_executable': sys.executable,
        'working_directory': os.getcwd()
    }

def main():
    """Main launcher function"""
    print("🏪 ATE MEG's FROZEN FOODS POS System")
    print("=" * 50)
    print("🔧 Universal Launcher - Works on Any Device")
    print("=" * 50)
    
    # Show system info
    info = get_system_info()
    print(f"🖥️  Platform: {info['platform']}")
    print(f"🐍 Python: {info['python_version']}")
    print(f"📁 Directory: {info['working_directory']}")
    print()
    
    # Check Python version
    print("1️⃣ Checking Python version...")
    if not check_python_version():
        input("Press Enter to exit...")
        return
    print("   ✅ Python version is compatible")
    
    # Check dependencies
    print("\n2️⃣ Checking dependencies...")
    if not check_dependencies():
        print("\n💡 To install dependencies automatically, run:")
        print("   pip install flask flask-sqlalchemy pandas")
        input("\nPress Enter to exit...")
        return
    print("   ✅ All dependencies are installed")
    
    # Check if app files exist
    print("\n3️⃣ Checking application files...")
    required_files = ['app.py', 'start_app.py', 'init_data.py']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ Missing application files:")
        for file in missing_files:
            print(f"   - {file}")
        input("Press Enter to exit...")
        return
    print("   ✅ All application files found")
    
    # Start the application
    print("\n4️⃣ Starting application...")
    print("🚀 Launching ATE MEG's FROZEN FOODS POS System...")
    print("=" * 50)
    
    try:
        # Use the same Python executable that's running this script
        subprocess.run([sys.executable, "start_app.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Error starting application: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure all files are in the correct directory")
        print("2. Check if port 5000 is available")
        print("3. Try running: python init_data.py")
        input("\nPress Enter to exit...")
    except KeyboardInterrupt:
        print("\n\n🛑 Application stopped by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print(f"\n🔧 System Info:")
        for key, value in info.items():
            print(f"   {key}: {value}")
        input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
