#!/usr/bin/env python3
"""
Migration script to add transaction_id to Sale table and populate existing data
"""

from app import app, db, Sale, Transaction, TransactionItem
from sqlalchemy import text

def migrate_add_transaction_id():
    """Add transaction_id column to Sale table and populate existing data"""
    with app.app_context():
        print("🔄 Starting migration: Adding transaction_id to Sale table...")
        
        try:
            # Check if transaction_id column already exists
            result = db.session.execute(text("PRAGMA table_info(sale)")).fetchall()
            columns = [row[1] for row in result]
            
            if 'transaction_id' in columns:
                print("✅ transaction_id column already exists")
            else:
                print("📝 Adding transaction_id column to Sale table...")
                # Add the new column
                db.session.execute(text("ALTER TABLE sale ADD COLUMN transaction_id INTEGER"))
                db.session.commit()
                print("✅ transaction_id column added successfully")
            
            # Now populate existing Sale records with transaction_id
            print("🔗 Linking existing Sale records to Transactions...")
            
            # Get all sales that don't have transaction_id set
            unlinked_sales = Sale.query.filter(Sale.transaction_id.is_(None)).all()
            print(f"📊 Found {len(unlinked_sales)} unlinked sales")
            
            linked_count = 0
            for sale in unlinked_sales:
                # Find matching transaction item
                matching_item = TransactionItem.query.filter(
                    TransactionItem.product_id == sale.product_id,
                    TransactionItem.quantity == sale.quantity,
                    TransactionItem.unit_price == sale.unit_price,
                    TransactionItem.total_price == sale.total_price
                ).join(Transaction).filter(
                    # Match by date proximity (within 1 day)
                    db.func.abs(
                        db.func.julianday(Transaction.transaction_date) - 
                        db.func.julianday(sale.sale_date)
                    ) < 1
                ).first()
                
                if matching_item:
                    sale.transaction_id = matching_item.transaction_id
                    linked_count += 1
                    print(f"   ✅ Linked Sale {sale.id} to Transaction {matching_item.transaction_id}")
                else:
                    print(f"   ⚠️  Could not find matching transaction for Sale {sale.id}")
            
            # Commit all changes
            db.session.commit()
            print(f"✅ Migration completed! Linked {linked_count} sales to transactions")
            
            # Verify the migration
            print("\n📊 Migration Verification:")
            total_sales = Sale.query.count()
            linked_sales = Sale.query.filter(Sale.transaction_id.isnot(None)).count()
            unlinked_sales = total_sales - linked_sales
            
            print(f"   Total Sales: {total_sales}")
            print(f"   Linked Sales: {linked_sales}")
            print(f"   Unlinked Sales: {unlinked_sales}")
            
            if unlinked_sales == 0:
                print("🎉 All sales are now properly linked to transactions!")
            else:
                print(f"⚠️  {unlinked_sales} sales could not be linked (possibly orphaned)")
                
        except Exception as e:
            db.session.rollback()
            print(f"❌ Migration failed: {str(e)}")
            raise

if __name__ == "__main__":
    migrate_add_transaction_id()
