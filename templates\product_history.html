{% extends "base.html" %} 
{% block title %}{{ product.name }} - Transaction History{% endblock %} 
{% block content %}

<style>
  .timeline {
    position: relative;
    padding-left: 30px;
  }
  
  .timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
  }
  
  .timeline-item {
    position: relative;
    margin-bottom: 20px;
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  
  .timeline-item::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 20px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
  }
  
  .timeline-item.stock-add::before { background: #28a745; }
  .timeline-item.stock-remove::before { background: #dc3545; }
  .timeline-item.price-change::before { background: #ffc107; }
  .timeline-item.sale::before { background: #17a2b8; }
  
  .transaction-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 10px;
  }
  
  .transaction-type {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
  }
  
  .transaction-date {
    color: #6c757d;
    font-size: 0.85rem;
  }
</style>

<div class="row">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h1><i class="fas fa-history"></i> Transaction History</h1>
        <h4 class="text-muted">{{ product.name }}</h4>
      </div>
      <div>
        <a href="{{ url_for('edit_product', id=product.id) }}" class="btn btn-primary me-2">
          <i class="fas fa-edit"></i> Edit Product
        </a>
        <a href="{{ url_for('products') }}" class="btn btn-secondary">
          <i class="fas fa-arrow-left"></i> Back to Products
        </a>
      </div>
    </div>
  </div>
</div>

<!-- Current Status Card -->
<div class="row mb-4">
  <div class="col-12">
    <div class="card">
      <div class="card-header bg-primary text-white">
        <h5 class="mb-0"><i class="fas fa-info-circle"></i> Current Status</h5>
      </div>
      <div class="card-body">
        <div class="row text-center">
          <div class="col-md-3">
            <h3 class="text-primary">{{ product.stock }}</h3>
            <small class="text-muted">Current Stock</small>
          </div>
          <div class="col-md-3">
            <h3 class="text-info">{{ product.received }}</h3>
            <small class="text-muted">Total Received</small>
          </div>
          <div class="col-md-3">
            <h3 class="text-success">{{ product.sold }}</h3>
            <small class="text-muted">Total Sold</small>
          </div>
          <div class="col-md-3">
            <h3 class="text-warning">₱{{ "%.2f"|format(product.price) }}</h3>
            <small class="text-muted">Current Price</small>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Transaction Timeline -->
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-clock"></i> Transaction Timeline</h5>
      </div>
      <div class="card-body">
        {% if transactions or sales %}
        <div class="timeline">
          <!-- Combine and sort transactions and sales -->
          {% set all_events = [] %}
          
          <!-- Add transactions -->
          {% for transaction in transactions %}
            {% set _ = all_events.append({
              'type': 'transaction',
              'data': transaction,
              'date': transaction.created_at
            }) %}
          {% endfor %}
          
          <!-- Add sales -->
          {% for sale in sales %}
            {% set _ = all_events.append({
              'type': 'sale', 
              'data': sale,
              'date': sale.sale_date
            }) %}
          {% endfor %}
          
          <!-- Sort by date (most recent first) -->
          {% set sorted_events = all_events|sort(attribute='date', reverse=true) %}
          
          {% for event in sorted_events %}
            {% if event.type == 'transaction' %}
              {% set transaction = event.data %}
              <div class="timeline-item {{ transaction.transaction_type }}">
                <div class="transaction-header">
                  <div>
                    {% if transaction.transaction_type == 'stock_add' %}
                      <span class="transaction-type text-success">
                        <i class="fas fa-plus-circle"></i> Stock Added
                      </span>
                    {% elif transaction.transaction_type == 'stock_remove' %}
                      <span class="transaction-type text-danger">
                        <i class="fas fa-minus-circle"></i> Stock Removed
                      </span>
                    {% elif transaction.transaction_type == 'price_change' %}
                      <span class="transaction-type text-warning">
                        <i class="fas fa-tag"></i> Price Changed
                      </span>
                    {% endif %}
                  </div>
                  <div class="transaction-date">
                    {{ transaction.created_at.strftime('%Y-%m-%d %H:%M') }}
                  </div>
                </div>
                
                <div class="transaction-details">
                  {% if transaction.transaction_type in ['stock_add', 'stock_remove'] %}
                    <strong>Quantity: {{ transaction.quantity_change|abs }} units</strong>
                    {% if transaction.reason %}
                      <br><small class="text-muted">Reason: {{ transaction.reason.replace('_', ' ').title() }}</small>
                    {% endif %}
                  {% elif transaction.transaction_type == 'price_change' %}
                    <strong>Selling Price:</strong> ₱{{ "%.2f"|format(transaction.old_price) }} → ₱{{ "%.2f"|format(transaction.new_price) }}<br>
                    <strong>Capital Price:</strong> ₱{{ "%.2f"|format(transaction.old_capital_price) }} → ₱{{ "%.2f"|format(transaction.new_capital_price) }}
                  {% endif %}
                  
                  {% if transaction.notes %}
                    <br><small class="text-muted">{{ transaction.notes }}</small>
                  {% endif %}
                </div>
              </div>
            {% elif event.type == 'sale' %}
              {% set sale = event.data %}
              <div class="timeline-item sale">
                <div class="transaction-header">
                  <div>
                    <span class="transaction-type text-info">
                      <i class="fas fa-shopping-cart"></i> Sale
                    </span>
                  </div>
                  <div class="transaction-date">
                    {{ sale.sale_date.strftime('%Y-%m-%d %H:%M') }}
                  </div>
                </div>
                
                <div class="transaction-details">
                  <strong>Sold: {{ sale.quantity }} units</strong> at ₱{{ "%.2f"|format(sale.unit_price) }} each<br>
                  <strong>Total: ₱{{ "%.2f"|format(sale.total_price) }}</strong>
                </div>
              </div>
            {% endif %}
          {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-5">
          <i class="fas fa-history fa-3x text-muted mb-3"></i>
          <h4 class="text-muted">No Transaction History</h4>
          <p class="text-muted">This product has no recorded transactions yet.</p>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

{% endblock %}
