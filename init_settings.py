#!/usr/bin/env python3
"""
Initialize Settings table and add default reseller discount
"""

from app import app, db, Settings

def init_settings():
    """Initialize the Settings table with default values"""
    with app.app_context():
        print("🔧 Initializing Settings table...")
        
        try:
            # Create the Settings table if it doesn't exist
            db.create_all()
            print("✅ Settings table created/verified")
            
            # Check if reseller discount setting already exists
            existing_setting = Settings.query.filter_by(key='reseller_discount').first()
            
            if not existing_setting:
                # Create default reseller discount setting
                default_discount = Settings(
                    key='reseller_discount',
                    value='5.0',
                    description='Discount amount in pesos for reseller customers'
                )
                db.session.add(default_discount)
                db.session.commit()
                print("✅ Default reseller discount (₱5.00) added to settings")
            else:
                print(f"✅ Reseller discount already exists: ₱{existing_setting.value}")
            
            print("🎉 Settings initialization complete!")
            
        except Exception as e:
            print(f"❌ Error initializing settings: {str(e)}")
            db.session.rollback()

if __name__ == "__main__":
    init_settings()
