#!/usr/bin/env python3
"""
Recreate database with updated schema including capital_price
"""

from app import app, db, Product, Customer, Sale, Transaction, TransactionItem, CreditTransaction
import os

def recreate_database():
    """Recreate the database with the new schema"""
    with app.app_context():
        print("🗃️ Recreating database with updated schema...")
        
        # Remove existing database
        db_path = 'frozen_foods_pos.db'
        if os.path.exists(db_path):
            os.remove(db_path)
            print("🗑️ Removed old database")
        
        # Create all tables with new schema
        db.create_all()
        print("✅ Created new database with updated schema")
        
        # Add sample products with capital prices
        products_data = [
            {"name": "Chicken Pastil", "price": 110.00, "capital_price": 75.00, "received": 30, "sold": 21, "stock": 9},
            {"name": "Pork Sisig", "price": 110.00, "capital_price": 75.00, "received": 27, "sold": 20, "stock": 7},
            {"name": "Cheesy Hamonado", "price": 90.00, "capital_price": 60.00, "received": 32, "sold": 21, "stock": 11},
            {"name": "Pork Dinakdakan", "price": 110.00, "capital_price": 75.00, "received": 24, "sold": 15, "stock": 9},
            {"name": "Chicken Teriyaki", "price": 80.00, "capital_price": 55.00, "received": 23, "sold": 15, "stock": 8},
            {"name": "Chicken Tocino", "price": 80.00, "capital_price": 55.00, "received": 21, "sold": 13, "stock": 8},
            {"name": "Pork Tapa", "price": 75.00, "capital_price": 50.00, "received": 28, "sold": 22, "stock": 6},
            {"name": "Pork Longadog", "price": 65.00, "capital_price": 45.00, "received": 25, "sold": 19, "stock": 6},
            {"name": "Pork Teriyaki", "price": 75.00, "capital_price": 50.00, "received": 22, "sold": 15, "stock": 7},
            {"name": "Pork Tocino", "price": 75.00, "capital_price": 50.00, "received": 24, "sold": 16, "stock": 8},
            {"name": "Skinless Longanisa", "price": 65.00, "capital_price": 45.00, "received": 23, "sold": 16, "stock": 7},
            {"name": "Pork Longanisa", "price": 65.00, "capital_price": 45.00, "received": 22, "sold": 15, "stock": 7},
            {"name": "Big Siomai", "price": 70.00, "capital_price": 45.00, "received": 30, "sold": 22, "stock": 8},
            {"name": "Salami w/ Cheese", "price": 90.00, "capital_price": 60.00, "received": 18, "sold": 11, "stock": 7},
            {"name": "Pork Meatballs", "price": 60.00, "capital_price": 40.00, "received": 19, "sold": 11, "stock": 8},
            {"name": "Chicken Nuggets", "price": 60.00, "capital_price": 40.00, "received": 21, "sold": 20, "stock": 1},
            {"name": "Sliced Ham", "price": 75.00, "capital_price": 50.00, "received": 16, "sold": 9, "stock": 7},
            {"name": "Beef Burger Patty", "price": 80.00, "capital_price": 55.00, "received": 15, "sold": 8, "stock": 7}
        ]
        
        print("📦 Adding products with capital prices...")
        for product_data in products_data:
            product = Product(**product_data)
            db.session.add(product)
        
        # Add sample customers
        customers_data = [
            {"name": "Maria Santos", "phone": "09123456789", "address": "123 Main St"},
            {"name": "Juan Dela Cruz", "phone": "09234567890", "address": "456 Oak Ave"},
            {"name": "Ana Garcia", "phone": "09345678901", "address": "789 Pine Rd"},
            {"name": "Pedro Reyes", "phone": "09456789012", "address": "321 Elm St"},
            {"name": "Rosa Martinez", "phone": "09567890123", "address": "654 Maple Dr"}
        ]
        
        print("👥 Adding customers...")
        for customer_data in customers_data:
            customer = Customer(**customer_data)
            db.session.add(customer)
        
        db.session.commit()
        print("✅ Database recreated successfully!")
        
        # Verify the data
        print("\n📊 Product Data with Profit Calculations:")
        print(f"{'Product':<20} {'Selling':<8} {'Capital':<8} {'Profit/Unit':<10} {'Received':<8} {'Sold':<6} {'Stock':<6} {'Total Profit':<12}")
        print("-" * 90)
        
        for product in Product.query.all():
            print(f"{product.name:<20} ₱{product.price:<7.2f} ₱{product.capital_price:<7.2f} ₱{product.profit_per_unit:<9.2f} {product.received:<8} {product.sold:<6} {product.stock:<6} ₱{product.total_profit:<11.2f}")

if __name__ == "__main__":
    recreate_database()