{% extends "base.html" %} {% block title %}Transaction History - Frozen Foods
POS{% endblock %} {% block extra_css %}
<style>
  .transaction-table .currency-col {
    min-width: 120px;
    white-space: nowrap;
  }
  .transaction-table td.currency-col {
    font-weight: 600;
  }
</style>
{% endblock %} {% block content %}
<div class="row">
  <div class="col-12">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h1><i class="fas fa-receipt"></i> Transaction History</h1>
      <a href="{{ url_for('reports') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Reports
      </a>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="fas fa-history"></i> Recent Transactions (Last 100)
        </h5>
      </div>
      <div class="card-body">
        {% if transactions %}
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead class="table-dark">
              <tr>
                <th class="text-nowrap">Transaction #</th>
                <th class="text-nowrap">Date</th>
                <th class="text-nowrap">Cashier</th>
                <th class="text-nowrap">Customer Type</th>
                <th class="text-nowrap">Payment</th>
                <th class="text-nowrap">Items</th>
                <th class="text-nowrap" style="min-width: 120px">
                  Total Amount
                </th>
                <th class="text-nowrap" style="min-width: 140px">Actions</th>
              </tr>
            </thead>
            <tbody>
              {% for transaction in transactions %}
              <tr>
                <td>
                  <strong>{{ transaction.transaction_number }}</strong>
                </td>
                <td>{{ transaction.transaction_date.strftime('%Y-%m-%d') }}</td>
                <td>
                  <span class="badge bg-primary"
                    >{{ transaction.cashier.name if transaction.cashier else 'No
                    Cashier' }}</span
                  >
                </td>
                <td>
                  <span
                    class="badge {{ 'bg-warning' if transaction.customer_type == 'reseller' else 'bg-secondary' }}"
                  >
                    {{ transaction.customer_type.title() if
                    transaction.customer_type else 'Default' }}
                  </span>
                </td>
                <td>
                  <span
                    class="badge {{ 'bg-success' if transaction.payment_method == 'cash' else 'bg-info' }}"
                  >
                    {{ transaction.payment_method.title() if
                    transaction.payment_method else 'Cash' }}
                  </span>
                </td>
                <td>
                  <span class="badge bg-info"
                    >{{ transaction.items|length }} item(s)</span
                  >
                </td>
                <td class="text-success text-nowrap">
                  <strong>{{ transaction.total_amount|currency }}</strong>
                </td>
                <td class="text-nowrap">
                  <div class="d-flex gap-1">
                    <button
                      class="btn btn-sm btn-outline-primary"
                      onclick="viewTransactionDetails('{{ transaction.id }}')"
                      data-bs-toggle="modal"
                      data-bs-target="#transactionModal"
                      title="View Details"
                    >
                      <i class="fas fa-eye"></i>
                      <span class="d-none d-md-inline"> View</span>
                    </button>
                    <button
                      class="btn btn-sm btn-outline-danger"
                      onclick="deleteTransaction('{{ transaction.transaction_number }}')"
                      title="Delete Transaction"
                    >
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        {% else %}
        <div class="text-center py-5">
          <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
          <h4 class="text-muted">No transactions found</h4>
          <p class="text-muted">
            Start making sales to see transaction history here.
          </p>
          <a href="{{ url_for('pos') }}" class="btn btn-primary">
            <i class="fas fa-cash-register"></i> Go to POS
          </a>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- Transaction Summary -->
{% if transactions %}
<div class="row mt-4">
  <div class="col-md-3">
    <div class="card text-white bg-primary">
      <div class="card-body text-center">
        <h5>Total Transactions</h5>
        <h3>{{ transactions|length }}</h3>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-success">
      <div class="card-body text-center">
        <h5>Total Revenue</h5>
        <h3>{{ transactions|sum(attribute='total_amount')|currency }}</h3>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-info">
      <div class="card-body text-center">
        <h5>Average Sale</h5>
        <h3>
          {{ ((transactions|sum(attribute='total_amount') / transactions|length)
          if transactions else 0)|currency }}
        </h3>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card text-white bg-warning">
      <div class="card-body text-center">
        <h5>Today's Sales</h5>
        <h3>
          {{
          (transactions|selectattr('transaction_date')|map(attribute='total_amount')|sum
          if transactions else 0)|currency }}
        </h3>
      </div>
    </div>
  </div>
</div>
{% endif %}

<!-- Transaction Details Modal -->
<div class="modal fade" id="transactionModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="fas fa-receipt"></i> Transaction Details
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body" id="transactionDetails">
        <div class="text-center">
          <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Close
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block scripts %}
<script>
  function viewTransactionDetails(transactionId) {
      const detailsContainer = document.getElementById('transactionDetails');

      // Show loading spinner
      detailsContainer.innerHTML = `
          <div class="text-center">
              <div class="spinner-border" role="status">
                  <span class="visually-hidden">Loading...</span>
              </div>
          </div>
      `;

      // Find transaction data from the current page
      const transactionData = [
          {% for transaction in transactions %}
          {
              id: {{ transaction.id }},
              transaction_number: "{{ transaction.transaction_number }}",
              total_amount: {{ transaction.total_amount }},
              transaction_date: "{{ transaction.transaction_date.isoformat() }}",
              cashier_name: "{{ transaction.cashier.name if transaction.cashier else 'No Cashier' }}",
              items: [
                  {% for item in transaction.items %}
                  {
                      product: { name: "{{ item.product.name }}" },
                      quantity: {{ item.quantity }},
                      unit_price: {{ item.unit_price }},
                      total_price: {{ item.total_price }}
                  }{% if not loop.last %},{% endif %}
                  {% endfor %}
              ]
          }{% if not loop.last %},{% endif %}
          {% endfor %}
      ];
      const transaction = transactionData.find(t => t.id == transactionId);

      if (transaction) {
          let itemsHtml = '';
          let totalItems = 0;

          transaction.items.forEach(item => {
              totalItems += item.quantity;
              itemsHtml += `
                  <tr>
                      <td>${item.product.name}</td>
                      <td>${item.quantity}</td>
                      <td>₱${item.unit_price.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                      <td class="text-success">₱${item.total_price.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                  </tr>
              `;
          });

          detailsContainer.innerHTML = `
              <div class="row mb-3">
                  <div class="col-md-6">
                      <strong>Transaction Number:</strong><br>
                      <span class="text-primary">${transaction.transaction_number}</span>
                  </div>
                  <div class="col-md-6">
                      <strong>Date & Time:</strong><br>
                      ${new Date(transaction.transaction_date).toLocaleString()}
                  </div>
              </div>

              <div class="row mb-3">
                  <div class="col-md-4">
                      <strong>Cashier:</strong><br>
                      <span class="badge bg-primary">${transaction.cashier_name}</span>
                  </div>
                  <div class="col-md-4">
                      <strong>Total Items:</strong><br>
                      <span class="badge bg-info">${totalItems} item(s)</span>
                  </div>
                  <div class="col-md-4">
                      <strong>Total Amount:</strong><br>
                      <span class="text-success h5">₱${transaction.total_amount.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span>
                  </div>
              </div>

              <h6 class="mt-4 mb-3">Items Purchased:</h6>
              <div class="table-responsive">
                  <table class="table table-striped">
                      <thead class="table-dark">
                          <tr>
                              <th>Product</th>
                              <th>Quantity</th>
                              <th>Unit Price</th>
                              <th>Total</th>
                          </tr>
                      </thead>
                      <tbody>
                          ${itemsHtml}
                      </tbody>
                      <tfoot class="table-secondary">
                          <tr>
                              <th colspan="3">TOTAL</th>
                              <th class="text-success">₱${transaction.total_amount.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</th>
                          </tr>
                      </tfoot>
                  </table>
              </div>
          `;
      } else {
          detailsContainer.innerHTML = `
              <div class="alert alert-danger">
                  <i class="fas fa-exclamation-triangle"></i>
                  Transaction details not found.
              </div>
          `;
      }
  }

  function deleteTransaction(transactionNumber) {
      if (confirm(`Are you sure you want to delete transaction ${transactionNumber}? This will restore the stock and cannot be undone.`)) {
          // Create a form and submit it
          const form = document.createElement('form');
          form.method = 'POST';
          form.action = `/delete_transaction/${transactionNumber}`;

          // Add CSRF token if needed (Flask-WTF)
          const csrfToken = document.querySelector('meta[name=csrf-token]');
          if (csrfToken) {
              const csrfInput = document.createElement('input');
              csrfInput.type = 'hidden';
              csrfInput.name = 'csrf_token';
              csrfInput.value = csrfToken.getAttribute('content');
              form.appendChild(csrfInput);
          }

          document.body.appendChild(form);
          form.submit();
      }
  }
</script>
{% endblock %}
