#!/usr/bin/env python3
"""
Display current inventory with updated prices
"""

from app import app, db, Product

def show_current_inventory():
    """Display current inventory with all details"""
    
    with app.app_context():
        print("🏪 ATE MEG's FROZEN FOODS - Current Inventory")
        print("=" * 60)
        
        products = Product.query.order_by(Product.name).all()
        
        print(f"{'Product':<25} {'Price':<8} {'Stock':<6} {'Sold':<6} {'Received':<8}")
        print("-" * 60)
        
        total_value = 0
        total_stock = 0
        total_sold = 0
        
        for product in products:
            stock_value = product.stock * product.price
            total_value += stock_value
            total_stock += product.stock
            total_sold += product.sold
            
            print(f"{product.name:<25} ₱{product.price:<7.0f} {product.stock:<6} {product.sold:<6} {product.received:<8}")
        
        print("-" * 60)
        print(f"{'TOTALS':<25} {'':8} {total_stock:<6} {total_sold:<6}")
        print(f"\nTotal Inventory Value: ₱{total_value:,.2f}")
        print(f"Total Products: {len(products)}")
        print(f"Products in Stock: {len([p for p in products if p.stock > 0])}")
        print(f"Out of Stock: {len([p for p in products if p.stock == 0])}")
        print(f"Low Stock (≤5): {len([p for p in products if p.stock <= 5 and p.stock > 0])}")
        
        # Price range analysis
        prices = [p.price for p in products]
        print(f"\nPrice Range:")
        print(f"Lowest Price: ₱{min(prices):.0f}")
        print(f"Highest Price: ₱{max(prices):.0f}")
        print(f"Average Price: ₱{sum(prices)/len(prices):.0f}")

if __name__ == "__main__":
    show_current_inventory()
