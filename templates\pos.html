{% extends "base.html" %}

{% block title %}Point of Sale - Frozen Foods POS{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-cash-register"></i> Point of Sale
        </h1>
    </div>
</div>

<div class="row">
    <!-- Products Section -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-box"></i> Available Products
                </h5>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge bg-info" id="product-count">{{ products|length }} products</span>
                    <span class="badge bg-success" id="filtered-count" style="display: none;">0 found</span>
                </div>
            </div>
            <div class="card-body">
                <!-- Search and Filter Section -->
                <div class="row mb-3">
                    <div class="col-md-8">
                        <div class="search-container">
                            <div class="search-input-wrapper">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" class="form-control search-input" id="product-search"
                                       placeholder="Search products by name..." autocomplete="off">
                                <button class="clear-btn" type="button" id="clear-search">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" id="stock-filter">
                            <option value="all">All Products</option>
                            <option value="in-stock">In Stock Only</option>
                            <option value="low-stock">Low Stock (≤5)</option>
                            <option value="out-of-stock">Out of Stock</option>
                        </select>
                    </div>
                </div>
                {% if products %}
                <!-- Desktop/Tablet View - Compact Grid -->
                <div class="row d-none d-md-flex" id="products-grid">
                    {% for product in products %}
                    <div class="col-xl-3 col-lg-4 col-md-6 mb-2 product-item"
                         data-product-id="{{ product.id }}"
                         data-product-name="{{ product.name }}"
                         data-product-price="{{ product.price }}"
                         data-product-stock="{{ product.stock }}"
                         data-search-text="{{ product.name|lower }}">
                        <div class="card product-card h-100 compact-card">
                            <div class="card-body text-center p-2">
                                <h6 class="card-title mb-1 product-name">{{ product.name }}</h6>
                                <div class="price-stock-row mb-2">
                                    <span class="price-tag">{{ product.price|currency }}</span>
                                    <span class="stock-badge {% if product.stock <= 5 %}stock-low{% elif product.stock == 0 %}stock-out{% else %}stock-ok{% endif %}">
                                        {{ product.stock }}
                                    </span>
                                </div>
                                <div class="quantity-controls-compact mb-2">
                                    <div class="qty-row">
                                        <button class="btn btn-outline-secondary btn-sm qty-minus" type="button" data-product-id="{{ product.id }}" {% if product.stock == 0 %}disabled{% endif %}>
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="number" class="form-control form-control-sm text-center qty-input-compact"
                                               id="qty-{{ product.id }}" value="1" min="1" max="{{ product.stock if product.stock > 0 else 1 }}"
                                               data-product-id="{{ product.id }}" {% if product.stock == 0 %}disabled{% endif %}>
                                        <button class="btn btn-outline-secondary btn-sm qty-plus" type="button" data-product-id="{{ product.id }}" {% if product.stock == 0 %}disabled{% endif %}>
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                    <div class="quick-qty-compact">
                                        <button class="btn btn-outline-info btn-xs quick-qty-btn"
                                                data-product-id="{{ product.id }}" data-qty="5"
                                                {% if product.stock < 5 %}disabled{% endif %}>5</button>
                                        <button class="btn btn-outline-info btn-xs quick-qty-btn"
                                                data-product-id="{{ product.id }}" data-qty="10"
                                                {% if product.stock < 10 %}disabled{% endif %}>10</button>
                                    </div>
                                </div>
                                <button class="btn btn-primary btn-sm add-to-cart w-100 compact-btn"
                                        data-product-id="{{ product.id }}"
                                        {% if product.stock == 0 %}disabled{% endif %}>
                                    {% if product.stock == 0 %}
                                        <i class="fas fa-times"></i> Out
                                    {% else %}
                                        <i class="fas fa-cart-plus"></i> Add
                                    {% endif %}
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- No Results Message -->
                <div id="no-results" class="text-center py-4" style="display: none;">
                    <i class="fas fa-search fa-2x text-muted mb-2"></i>
                    <h5 class="text-muted">No products found</h5>
                    <p class="text-muted">Try adjusting your search or filter criteria.</p>
                </div>

                <!-- Mobile Compact View -->
                <div class="d-md-none mobile-products-grid">
                    {% for product in products %}
                    <div class="mobile-product-item" data-product-id="{{ product.id }}"
                         data-product-name="{{ product.name }}"
                         data-product-price="{{ product.price }}"
                         data-product-stock="{{ product.stock }}">
                        <div class="mobile-product-info">
                            <div class="mobile-product-name">{{ product.name }}</div>
                            <div class="mobile-product-price">{{ product.price|currency }}</div>
                            <div class="mobile-product-stock">Stock: <span id="mobile-stock-{{ product.id }}">{{ product.stock }}</span></div>
                        </div>
                        <div class="mobile-product-controls">
                            <div class="mobile-qty-controls">
                                <button class="btn btn-outline-secondary btn-sm qty-minus" type="button" data-product-id="{{ product.id }}" {% if product.stock == 0 %}disabled{% endif %}>
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" class="form-control form-control-sm text-center qty-input mobile-qty-input"
                                       id="mobile-qty-{{ product.id }}" value="1" min="1" max="{{ product.stock if product.stock > 0 else 1 }}"
                                       data-product-id="{{ product.id }}" {% if product.stock == 0 %}disabled{% endif %}>
                                <button class="btn btn-outline-secondary btn-sm qty-plus" type="button" data-product-id="{{ product.id }}" {% if product.stock == 0 %}disabled{% endif %}>
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <button class="btn btn-primary btn-sm add-to-cart mobile-add-btn"
                                    data-product-id="{{ product.id }}"
                                    {% if product.stock == 0 %}disabled{% endif %}>
                                {% if product.stock == 0 %}
                                    <i class="fas fa-times"></i>
                                {% else %}
                                    <i class="fas fa-cart-plus"></i>
                                {% endif %}
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No products available</h4>
                    <p class="text-muted">Add products to your inventory to start selling.</p>
                    <a href="{{ url_for('add_product') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Products
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Cart Section -->
    <div class="col-md-4">
        <!-- Credit Summary Card -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-credit-card"></i> Credit Summary
                    <a href="{{ url_for('credit_management') }}" class="btn btn-sm btn-outline-info float-end">
                        <i class="fas fa-external-link-alt"></i> Manage
                    </a>
                </h6>
            </div>
            <div class="card-body p-2">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="text-danger">
                            <small>Outstanding</small><br>
                            <strong id="total-outstanding">₱0.00</strong>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="text-success">
                            <small>Paid</small><br>
                            <strong id="total-paid">₱0.00</strong>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="text-warning">
                            <small>Customers</small><br>
                            <strong id="customers-with-credit">0</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card pos-container">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shopping-cart"></i> Shopping Cart
                </h5>
            </div>
            <div class="card-body shopping-cart">
                <!-- Customer Selection -->
                <div class="mb-3">
                    <label for="customer-select" class="form-label">Customer (Optional)</label>
                    <select class="form-select" id="customer-select" onchange="updateCustomerInfo()">
                        <option value="">Cash Sale</option>
                        {% for customer in customers %}
                        <option value="{{ customer.id }}"
                                data-balance="{{ customer.balance }}"
                                data-total-credit="{{ customer.total_credit }}"
                                data-total-paid="{{ customer.total_paid }}">
                            {{ customer.name }}
                            {% if customer.balance > 0 %}
                                ({{ customer.balance|currency }} outstanding)
                            {% endif %}
                        </option>
                        {% endfor %}
                    </select>
                    <div class="form-text">Select customer for credit sales (pautang)</div>

                    <!-- Customer Credit Info -->
                    <div id="customer-credit-info" class="mt-2" style="display: none;">
                        <div class="alert alert-info p-2">
                            <small>
                                <strong>Credit Info:</strong><br>
                                Total Credit: ₱<span id="customer-total-credit">0.00</span><br>
                                Total Paid: ₱<span id="customer-total-paid">0.00</span><br>
                                Outstanding: ₱<span id="customer-balance">0.00</span>
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Cashier Selection -->
                <div class="mb-3">
                    <label for="cashier-select" class="form-label">
                        <i class="fas fa-user"></i> Cashier
                    </label>
                    <div class="d-flex gap-2">
                        <select class="form-select" id="cashier-select" required>
                            <option value="">Select Cashier</option>
                        </select>
                        <button type="button" class="btn btn-outline-primary btn-sm" id="add-cashier-btn" title="Add New Cashier">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>

                <!-- Customer Type & Payment Method (Compact) -->
                <div class="mb-3">
                    <div class="row g-2">
                        <div class="col-6">
                            <label class="form-label small">Customer</label>
                            <div class="btn-group-vertical w-100" role="group">
                                <input type="radio" class="btn-check" name="customer-type" id="default-customer" value="default" checked>
                                <label class="btn btn-outline-primary btn-sm" for="default-customer">
                                    <i class="fas fa-user"></i> Default
                                </label>

                                <input type="radio" class="btn-check" name="customer-type" id="reseller-customer" value="reseller">
                                <label class="btn btn-outline-warning btn-sm" for="reseller-customer">
                                    <i class="fas fa-store"></i> Reseller
                                </label>
                            </div>
                            <small class="text-muted">Reseller: -{{ reseller_discount|currency }}</small>
                        </div>
                        <div class="col-6">
                            <label class="form-label small">Payment</label>
                            <div class="btn-group-vertical w-100" role="group">
                                <input type="radio" class="btn-check" name="payment-method" id="cash-payment" value="cash" checked>
                                <label class="btn btn-outline-success btn-sm" for="cash-payment">
                                    <i class="fas fa-money-bill"></i> Cash
                                </label>

                                <input type="radio" class="btn-check" name="payment-method" id="credit-payment" value="credit">
                                <label class="btn btn-outline-info btn-sm" for="credit-payment">
                                    <i class="fas fa-credit-card"></i> Credit
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cash Payment Details -->
                <div id="cash-payment-details" class="mb-2">
                    <label for="cash-received" class="form-label small">Cash Received (₱)</label>
                    <input type="number" class="form-control form-control-sm" id="cash-received"
                           step="0.01" min="0" placeholder="0.00">

                    <!-- Change Display -->
                    <div id="change-display" class="mt-2" style="display: none;">
                        <div class="alert alert-info alert-sm py-2">
                            <div class="d-flex justify-content-between small">
                                <span>Total:</span>
                                <span id="change-total">₱0.00</span>
                            </div>
                            <div class="d-flex justify-content-between small">
                                <span>Received:</span>
                                <span id="change-received">₱0.00</span>
                            </div>
                            <hr class="my-1">
                            <div class="d-flex justify-content-between">
                                <span><strong>Change:</strong></span>
                                <span id="change-amount" class="text-success fw-bold">₱0.00</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cart Items -->
                <div id="cart-items" class="mb-2">
                    <div class="text-center text-muted py-3" id="empty-cart">
                        <i class="fas fa-shopping-cart fa-lg mb-1"></i>
                        <p class="small mb-0">Cart is empty</p>
                    </div>
                </div>

                <!-- Cart Total -->
                <div class="cart-total bg-primary p-2 rounded mb-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="fw-bold text-white">Total:</span>
                        <span id="cart-total" class="fw-bold text-white">₱0.00</span>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="d-grid gap-1">
                    <button class="btn btn-success" id="process-sale" disabled>
                        <i class="fas fa-credit-card"></i> <span id="process-btn-text">Process Sale</span>
                    </button>
                    <div class="row g-1">
                        <div class="col-6">
                            <button class="btn btn-outline-secondary btn-sm w-100" id="clear-cart" disabled>
                                <i class="fas fa-trash"></i> Clear
                            </button>
                        </div>
                        <div class="col-6">
                            <button class="btn btn-outline-primary btn-sm w-100" id="backup-btn" title="Backup Database">
                                <i class="fas fa-download"></i> Backup
                            </button>
                        </div>
                    </div>
                    <button class="btn btn-outline-info btn-sm" id="mobile-help-btn" data-bs-toggle="collapse" data-bs-target="#mobile-help">
                        <i class="fas fa-info-circle"></i> Help
                    </button>
                </div>

                <!-- Help Section -->
                <div class="collapse mt-1" id="mobile-help">
                    <div class="card card-body py-2">
                        <div class="row g-2">
                            <div class="col-6">
                                <h6 class="small mb-1"><i class="fas fa-mobile-alt"></i> Steps</h6>
                                <small class="text-muted">
                                    1. Set quantity<br>
                                    2. Add to cart<br>
                                    3. Choose payment<br>
                                    4. Process sale
                                </small>
                            </div>
                            <div class="col-6">
                                <h6 class="small mb-1"><i class="fas fa-keyboard"></i> Shortcuts</h6>
                                <small class="text-muted">
                                    <strong>Ctrl+F:</strong> Search<br>
                                    <strong>Esc:</strong> Clear<br>
                                    <strong>5/10:</strong> Quick qty
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mobile Cart Summary Floating Button -->
<div class="mobile-cart-fab d-md-none" id="mobile-cart-fab">
    <button class="btn btn-primary rounded-circle" data-bs-toggle="offcanvas" data-bs-target="#mobile-cart">
        <i class="fas fa-shopping-cart"></i>
        <span class="cart-count badge bg-danger" id="mobile-cart-count">0</span>
    </button>
</div>

<!-- Mobile Cart Offcanvas -->
<div class="offcanvas offcanvas-bottom" tabindex="-1" id="mobile-cart">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title">Shopping Cart</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
    </div>
    <div class="offcanvas-body" id="mobile-cart-content">
        <!-- Cart content will be synced here -->
    </div>
</div>

<!-- Sale Success Modal -->
<div class="modal fade" id="saleSuccessModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle"></i> Sale Completed
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <h4>Transaction Successful!</h4>
                <p class="mb-1">Transaction Number: <strong id="transaction-number"></strong></p>
                <p class="mb-3">Total Amount: <strong id="transaction-total" class="text-success"></strong></p>
                <p class="text-muted">Thank you for your purchase!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                    <i class="fas fa-plus"></i> New Sale
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Receipt Modal -->
<div class="modal fade" id="receiptModal" tabindex="-1" aria-labelledby="receiptModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="receiptModalLabel">
                    <i class="fas fa-receipt"></i> Transaction Receipt
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <h6 class="mb-1">Frozen Foods POS</h6>
                    <small class="text-muted" id="receipt-date"></small>
                </div>

                <div class="border-bottom pb-2 mb-3">
                    <div class="row">
                        <div class="col-6"><strong>Transaction #:</strong></div>
                        <div class="col-6 text-end" id="receipt-transaction-number"></div>
                    </div>
                    <div class="row">
                        <div class="col-6"><strong>Payment Method:</strong></div>
                        <div class="col-6 text-end" id="receipt-payment-method"></div>
                    </div>
                    <div class="row" id="receipt-customer-row" style="display: none;">
                        <div class="col-6"><strong>Customer:</strong></div>
                        <div class="col-6 text-end" id="receipt-customer-name"></div>
                    </div>
                    <div class="row">
                        <div class="col-6"><strong>Cashier:</strong></div>
                        <div class="col-6 text-end" id="receipt-cashier-name"></div>
                    </div>
                </div>

                <div class="mb-3">
                    <h6>Items Purchased:</h6>
                    <div id="receipt-items"></div>
                </div>

                <div class="border-top pt-2">
                    <div class="row">
                        <div class="col-6"><strong>Total Amount:</strong></div>
                        <div class="col-6 text-end"><strong id="receipt-total"></strong></div>
                    </div>
                    <div class="row" id="receipt-cash-row" style="display: none;">
                        <div class="col-6">Cash Received:</div>
                        <div class="col-6 text-end" id="receipt-cash-received"></div>
                    </div>
                    <div class="row" id="receipt-change-row" style="display: none;">
                        <div class="col-6">Change:</div>
                        <div class="col-6 text-end" id="receipt-change"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" onclick="printReceipt()">
                    <i class="fas fa-print"></i> Print Receipt
                </button>
                <button type="button" class="btn btn-success" data-bs-dismiss="modal" onclick="startNewTransaction()">
                    <i class="fas fa-plus"></i> New Transaction
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Cashier Modal -->
<div class="modal fade" id="addCashierModal" tabindex="-1" aria-labelledby="addCashierModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCashierModalLabel">
                    <i class="fas fa-user-plus"></i> Add New Cashier
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="add-cashier-form">
                    <div class="mb-3">
                        <label for="cashier-name" class="form-label">Cashier Name</label>
                        <input type="text" class="form-control" id="cashier-name" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="save-cashier-btn">
                    <i class="fas fa-save"></i> Save Cashier
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let cart = [];

// Helper function for currency formatting
function formatCurrency(amount) {
    return `₱${parseFloat(amount).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
}

document.addEventListener('DOMContentLoaded', function() {
    // Load credit summary on page load
    loadCreditSummary();

    // Refresh product stock on page load
    refreshProductStock();

    // Add event listeners for customer type changes
    document.querySelectorAll('input[name="customer-type"]').forEach(radio => {
        radio.addEventListener('change', function() {
            updateCartDisplay(); // Recalculate cart with new pricing
        });
    });

    // Set up periodic stock refresh (every 30 seconds)
    setInterval(refreshProductStock, 30000);

    // Initialize search and filter functionality
    initializeSearchAndFilter();

    // Add to cart functionality
    document.querySelectorAll('.add-to-cart').forEach(button => {
        button.addEventListener('click', function() {

            const productId = parseInt(this.dataset.productId);
            // Look for the element that contains the data attributes
            const card = this.closest('.product-item') || this.closest('.mobile-product-item');

            if (!card) {
                showErrorNotification('Product container not found. Please refresh the page.');
                return;
            }

            const productName = card.dataset.productName;
            const productPrice = parseFloat(card.dataset.productPrice);
            const productStock = parseInt(card.dataset.productStock);



            // Validate parsed values
            if (isNaN(productId) || isNaN(productPrice) || isNaN(productStock)) {
                showErrorNotification('Invalid product data. Please refresh the page and try again.');
                console.error('Invalid product data:', {
                    productId: this.dataset.productId,
                    productPrice: card.dataset.productPrice,
                    productStock: card.dataset.productStock
                });
                return;
            }

            // Check for both desktop and mobile quantity inputs
            let qtyInput = document.getElementById(`qty-${productId}`) || document.getElementById(`mobile-qty-${productId}`);
            if (!qtyInput) {
                showErrorNotification('Quantity input not found. Please refresh the page.');
                return;
            }

            const quantity = parseInt(qtyInput.value);
            if (isNaN(quantity) || quantity <= 0) {
                showErrorNotification('Invalid quantity. Please enter a valid number.');
                return;
            }

            addToCart(productId, productName, productPrice, productStock, quantity);
        });
    });

    // Quantity controls - handle both desktop and mobile
    document.querySelectorAll('.qty-plus').forEach(button => {
        button.addEventListener('click', function() {
            const productId = parseInt(this.dataset.productId);
            let qtyInput = document.getElementById(`qty-${productId}`) || document.getElementById(`mobile-qty-${productId}`);
            const currentQty = parseInt(qtyInput.value);
            const maxStock = parseInt(qtyInput.max);

            if (currentQty < maxStock) {
                qtyInput.value = currentQty + 1;
                // Sync between desktop and mobile inputs
                syncQuantityInputs(productId, currentQty + 1);
            }
        });
    });

    document.querySelectorAll('.qty-minus').forEach(button => {
        button.addEventListener('click', function() {
            const productId = parseInt(this.dataset.productId);
            let qtyInput = document.getElementById(`qty-${productId}`) || document.getElementById(`mobile-qty-${productId}`);
            const currentQty = parseInt(qtyInput.value);

            if (currentQty > 1) {
                qtyInput.value = currentQty - 1;
                // Sync between desktop and mobile inputs
                syncQuantityInputs(productId, currentQty - 1);
            }
        });
    });

    // Quantity input validation
    document.querySelectorAll('.qty-input').forEach(input => {
        input.addEventListener('change', function() {
            const value = parseInt(this.value);
            const min = parseInt(this.min);
            const max = parseInt(this.max);

            if (value < min) this.value = min;
            if (value > max) this.value = max;

            // Sync between desktop and mobile inputs
            const productId = parseInt(this.dataset.productId);
            syncQuantityInputs(productId, this.value);
        });
    });

    // Quick quantity buttons
    document.querySelectorAll('.quick-qty-btn').forEach(button => {
        button.addEventListener('click', function() {
            const productId = parseInt(this.dataset.productId);
            const qty = parseInt(this.dataset.qty);
            const qtyInput = document.getElementById(`qty-${productId}`);

            qtyInput.value = qty;
            syncQuantityInputs(productId, qty);

            // Visual feedback
            this.classList.add('btn-primary');
            this.classList.remove('btn-outline-primary');
            setTimeout(() => {
                this.classList.remove('btn-primary');
                this.classList.add('btn-outline-primary');
            }, 500);
        });
    });

    // Process sale
    document.getElementById('process-sale').addEventListener('click', processSale);

    // Clear cart
    document.getElementById('clear-cart').addEventListener('click', clearCart);

    // Payment method change
    document.querySelectorAll('input[name="payment-method"]').forEach(radio => {
        radio.addEventListener('change', updatePaymentMethod);
    });

    // Customer selection change
    document.getElementById('customer-select').addEventListener('change', updateCustomerSelection);

    // Cash received input
    document.getElementById('cash-received').addEventListener('input', calculateChange);

    // Auto-focus on customer select when credit payment is selected
    updatePaymentMethod();

    // Load cashiers
    loadCashiers();

    // Add cashier button
    document.getElementById('add-cashier-btn').addEventListener('click', function() {
        const modal = new bootstrap.Modal(document.getElementById('addCashierModal'));
        modal.show();
    });

    // Save cashier button
    document.getElementById('save-cashier-btn').addEventListener('click', saveCashier);

    // Backup button
    document.getElementById('backup-btn').addEventListener('click', triggerFullBackup);
});

// Initialize search and filter functionality
function initializeSearchAndFilter() {
    const searchInput = document.getElementById('product-search');
    const stockFilter = document.getElementById('stock-filter');
    const clearSearchBtn = document.getElementById('clear-search');

    // Search functionality
    searchInput.addEventListener('input', function() {
        filterProducts();
    });

    // Stock filter functionality
    stockFilter.addEventListener('change', function() {
        filterProducts();
    });

    // Clear search functionality
    clearSearchBtn.addEventListener('click', function() {
        searchInput.value = '';
        stockFilter.value = 'all';
        filterProducts();
        searchInput.focus();
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + F to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            searchInput.focus();
            searchInput.select();
        }

        // Escape to clear search
        if (e.key === 'Escape' && searchInput === document.activeElement) {
            searchInput.value = '';
            filterProducts();
            searchInput.blur();
        }
    });
}

function filterProducts() {
    const searchTerm = document.getElementById('product-search').value.toLowerCase().trim();
    const stockFilter = document.getElementById('stock-filter').value;
    const productItems = document.querySelectorAll('.product-item');
    const noResults = document.getElementById('no-results');
    const filteredCount = document.getElementById('filtered-count');
    const productCount = document.getElementById('product-count');

    let visibleCount = 0;

    productItems.forEach(item => {
        const productName = item.dataset.searchText;
        const productStock = parseInt(item.dataset.productStock);

        // Check search term match
        const matchesSearch = !searchTerm || productName.includes(searchTerm);

        // Check stock filter match
        let matchesStock = true;
        switch (stockFilter) {
            case 'in-stock':
                matchesStock = productStock > 0;
                break;
            case 'low-stock':
                matchesStock = productStock <= 5 && productStock > 0;
                break;
            case 'out-of-stock':
                matchesStock = productStock === 0;
                break;
            default:
                matchesStock = true;
        }

        const shouldShow = matchesSearch && matchesStock;

        if (shouldShow) {
            item.style.display = 'block';
            visibleCount++;

            // Highlight search term
            if (searchTerm) {
                highlightSearchTerm(item, searchTerm);
            } else {
                removeHighlight(item);
            }
        } else {
            item.style.display = 'none';
        }
    });

    // Update counters and show/hide no results message
    if (searchTerm || stockFilter !== 'all') {
        filteredCount.textContent = `${visibleCount} found`;
        filteredCount.style.display = 'inline';
        productCount.style.display = 'none';
    } else {
        filteredCount.style.display = 'none';
        productCount.style.display = 'inline';
    }

    // Show/hide no results message
    if (visibleCount === 0) {
        noResults.style.display = 'block';
    } else {
        noResults.style.display = 'none';
    }
}

function highlightSearchTerm(item, searchTerm) {
    const nameElement = item.querySelector('.product-name');
    const originalText = nameElement.textContent;
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    const highlightedText = originalText.replace(regex, '<mark>$1</mark>');
    nameElement.innerHTML = highlightedText;
}

function removeHighlight(item) {
    const nameElement = item.querySelector('.product-name');
    const originalText = nameElement.textContent;
    nameElement.innerHTML = originalText;
}

// Sync quantity inputs between desktop and mobile views
function syncQuantityInputs(productId, value) {
    const desktopInput = document.getElementById(`qty-${productId}`);
    const mobileInput = document.getElementById(`mobile-qty-${productId}`);

    if (desktopInput) desktopInput.value = value;
    if (mobileInput) mobileInput.value = value;
}

function addToCart(productId, productName, productPrice, productStock, quantity = 1) {

    // Validate all parameters
    if (isNaN(productId) || isNaN(productPrice) || isNaN(productStock) || isNaN(quantity)) {
        showErrorNotification('Invalid product data. Please refresh the page and try again.');
        console.error('Invalid parameters in addToCart:', {
            productId,
            productName,
            productPrice,
            productStock,
            quantity
        });
        return;
    }

    // Validate quantity
    if (quantity <= 0) {
        showErrorNotification('Quantity must be greater than zero');
        return;
    }

    const existingItem = cart.find(item => item.product_id === productId);

    if (existingItem) {
        const newQuantity = existingItem.quantity + quantity;
        if (newQuantity <= productStock) {
            existingItem.quantity = newQuantity;
            showSuccessNotification(`Added ${quantity} more ${productName} to cart`);
        } else {
            const available = productStock - existingItem.quantity;
            showErrorNotification(`Cannot add ${quantity} more items. Only ${available} available.`);
            return;
        }
    } else {
        if (quantity <= productStock) {
            cart.push({
                product_id: productId,
                name: productName,
                price: productPrice,
                quantity: quantity,
                stock: productStock
            });
            showSuccessNotification(`Added ${quantity} ${productName} to cart`);
        } else {
            showErrorNotification(`Cannot add ${quantity} items. Only ${productStock} available.`);
            return;
        }
    }

    // Reset quantity inputs to 1 after adding (both desktop and mobile)
    const desktopQtyInput = document.getElementById(`qty-${productId}`);
    const mobileQtyInput = document.getElementById(`mobile-qty-${productId}`);

    if (desktopQtyInput) desktopQtyInput.value = 1;
    if (mobileQtyInput) mobileQtyInput.value = 1;

    // Update stock display immediately
    updateProductStockDisplay(productId, productStock - quantity);

    updateCartDisplay();

    // Show success feedback on button
    const button = document.querySelector(`[data-product-id="${productId}"].add-to-cart`);
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i> Added!';
    button.classList.add('btn-success');
    button.classList.remove('btn-primary');

    setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('btn-success');
        button.classList.add('btn-primary');
    }, 1000);
}

function updateCartDisplay() {
    const cartItemsContainer = document.getElementById('cart-items');
    const emptyCart = document.getElementById('empty-cart');
    const cartTotal = document.getElementById('cart-total');
    const processSaleBtn = document.getElementById('process-sale');
    const clearCartBtn = document.getElementById('clear-cart');

    if (cart.length === 0) {
        cartItemsContainer.innerHTML = '<div class="text-center text-muted py-4" id="empty-cart"><i class="fas fa-shopping-cart fa-2x mb-2"></i><p>Cart is empty</p></div>';
        cartTotal.textContent = formatCurrency(0);
        processSaleBtn.disabled = true;
        clearCartBtn.disabled = true;
        return;
    }

    let html = '';
    let total = 0;
    let totalItems = 0;

    // Check if reseller discount should be applied
    const customerType = document.querySelector('input[name="customer-type"]:checked')?.value || 'default';
    const resellerDiscount = customerType === 'reseller' ? {{ reseller_discount }} : 0;

    cart.forEach((item, index) => {
        let unitPrice = item.price;
        if (customerType === 'reseller') {
            unitPrice = Math.max(0, unitPrice - resellerDiscount);
        }
        const itemTotal = unitPrice * item.quantity;
        total += itemTotal;
        totalItems += item.quantity;

        const priceDisplay = customerType === 'reseller' && resellerDiscount > 0 ?
            `${formatCurrency(unitPrice)} each (${formatCurrency(item.price)} - ${formatCurrency(resellerDiscount)})` :
            `${formatCurrency(unitPrice)} each`;

        html += `
            <div class="cart-item mb-1 p-2 border rounded">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="flex-grow-1">
                        <div class="fw-bold text-primary small">${item.name}</div>
                        <div class="text-muted" style="font-size: 0.75rem;">${priceDisplay}</div>
                    </div>
                    <button class="btn btn-outline-danger btn-sm ms-2" onclick="removeFromCart(${index})" title="Remove">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-1">
                    <div class="btn-group btn-group-sm" role="group">
                        <button class="btn btn-outline-secondary btn-sm" onclick="updateQuantity(${index}, -1)">
                            <i class="fas fa-minus"></i>
                        </button>
                        <span class="btn btn-outline-secondary btn-sm disabled">${item.quantity}</span>
                        <button class="btn btn-outline-secondary btn-sm" onclick="updateQuantity(${index}, 1)">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <div class="fw-bold text-success">${formatCurrency(itemTotal)}</div>
                </div>
            </div>
        `;
    });

    // Add compact summary
    html += `
        <div class="cart-summary mt-2 p-2 bg-light rounded small">
            <div class="d-flex justify-content-between">
                <span>Items: <strong>${totalItems}</strong></span>
                <span class="fw-bold text-success">${formatCurrency(total)}</span>
            </div>
        </div>
    `;

    cartItemsContainer.innerHTML = html;
    cartTotal.textContent = formatCurrency(total);
    processSaleBtn.disabled = false;
    clearCartBtn.disabled = false;

    // Update mobile cart
    updateMobileCart(html, total, totalItems);

    // Update change calculation
    calculateChange();
}

function updateQuantity(index, change) {
    const item = cart[index];
    const newQuantity = item.quantity + change;

    if (newQuantity <= 0) {
        removeFromCart(index);
    } else if (newQuantity <= item.stock) {
        item.quantity = newQuantity;
        updateCartDisplay();

        // Show visual feedback
        const cartItems = document.querySelectorAll('.cart-item');
        if (cartItems[index]) {
            cartItems[index].style.backgroundColor = '#e8f5e8';
            setTimeout(() => {
                cartItems[index].style.backgroundColor = '';
            }, 500);
        }
    } else {
        showErrorNotification(`Cannot add more items. Maximum available: ${item.stock}`);

        // Show error feedback
        const cartItems = document.querySelectorAll('.cart-item');
        if (cartItems[index]) {
            cartItems[index].style.backgroundColor = '#ffe6e6';
            setTimeout(() => {
                cartItems[index].style.backgroundColor = '';
            }, 1000);
        }
    }
}

function removeFromCart(index) {
    const item = cart[index];

    // Confirm removal for items with quantity > 1
    if (item.quantity > 1) {
        if (!confirm(`Remove all ${item.quantity} ${item.name} from cart?`)) {
            return;
        }
    }

    cart.splice(index, 1);
    updateCartDisplay();

    // Show success message
    showToast(`${item.name} removed from cart`, 'success');
}

function clearCart() {
    if (cart.length > 0) {
        if (confirm('Clear all items from cart?')) {
            cart = [];
            updateCartDisplay();
            showToast('Cart cleared', 'info');
        }
    }
}

function updateMobileCart(html, total, totalItems) {
    // Update mobile cart count
    const mobileCartCount = document.getElementById('mobile-cart-count');
    const mobileCartContent = document.getElementById('mobile-cart-content');

    if (totalItems > 0) {
        mobileCartCount.textContent = totalItems;
        mobileCartCount.classList.remove('hidden');

        // Add mobile-specific cart content
        const mobileHtml = html + `
            <div class="mobile-cart-actions mt-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Total: ${formatCurrency(total)}</h5>
                </div>
                <div class="d-grid gap-2">
                    <button class="btn btn-success btn-lg" onclick="processSale()" ${cart.length === 0 ? 'disabled' : ''}>
                        <i class="fas fa-credit-card"></i> Process Sale
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearCart()">
                        <i class="fas fa-trash"></i> Clear Cart
                    </button>
                </div>
            </div>
        `;
        mobileCartContent.innerHTML = mobileHtml;
    } else {
        mobileCartCount.classList.add('hidden');
        mobileCartContent.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Cart is empty</h5>
                <p class="text-muted">Add products to start shopping</p>
            </div>
        `;
    }
}

function showToast(message, type = 'info') {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 250px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

function showErrorNotification(message) {
    // Create error notification
    const notification = document.createElement('div');
    notification.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    notification.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px; max-width: 500px;';
    notification.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i> <strong>Error:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

function showSuccessNotification(message) {
    // Create success notification
    const notification = document.createElement('div');
    notification.className = 'alert alert-success alert-dismissible fade show position-fixed';
    notification.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px; max-width: 500px;';
    notification.innerHTML = `
        <i class="fas fa-check-circle"></i> <strong>Success:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

function showWarningNotification(message) {
    // Create warning notification
    const notification = document.createElement('div');
    notification.className = 'alert alert-warning alert-dismissible fade show position-fixed';
    notification.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px; max-width: 500px;';
    notification.innerHTML = `
        <i class="fas fa-exclamation-circle"></i> <strong>Warning:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 4 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 4000);
}

function updatePaymentMethod() {
    const paymentMethodElement = document.querySelector('input[name="payment-method"]:checked');
    if (!paymentMethodElement) return;

    const paymentMethod = paymentMethodElement.value;
    const customerSelect = document.getElementById('customer-select');
    const processBtnText = document.getElementById('process-btn-text');
    const cashPaymentDetails = document.getElementById('cash-payment-details');

    // Add null checks for all elements
    if (!customerSelect || !processBtnText || !cashPaymentDetails) {
        console.warn('Some payment method elements not found');
        return;
    }

    if (paymentMethod === 'credit') {
        customerSelect.required = true;
        customerSelect.parentElement.classList.add('border', 'border-warning', 'rounded', 'p-2');
        processBtnText.textContent = 'Process Credit Sale';
        cashPaymentDetails.style.display = 'none';

        if (!customerSelect.value) {
            showWarningNotification('Please select a customer for credit sales');
        }
    } else {
        customerSelect.required = false;
        customerSelect.parentElement.classList.remove('border', 'border-warning', 'rounded', 'p-2');
        processBtnText.textContent = 'Process Cash Sale';
        cashPaymentDetails.style.display = 'block';
        calculateChange();
    }
}

function calculateChange() {
    const cashReceived = parseFloat(document.getElementById('cash-received').value) || 0;
    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const change = cashReceived - total;

    const changeDisplay = document.getElementById('change-display');
    const changeTotalEl = document.getElementById('change-total');
    const changeReceivedEl = document.getElementById('change-received');
    const changeAmountEl = document.getElementById('change-amount');

    if (cashReceived > 0) {
        changeDisplay.style.display = 'block';
        changeTotalEl.textContent = formatCurrency(total);
        changeReceivedEl.textContent = formatCurrency(cashReceived);
        changeAmountEl.textContent = formatCurrency(change);

        // Color coding for change
        if (change < 0) {
            changeAmountEl.className = 'text-danger fw-bold';
            changeAmountEl.textContent = `${formatCurrency(Math.abs(change))} (Insufficient)`;
        } else {
            changeAmountEl.className = 'text-success fw-bold';
        }
    } else {
        changeDisplay.style.display = 'none';
    }
}

function updateCustomerSelection() {
    const customerSelect = document.getElementById('customer-select');
    const creditRadio = document.getElementById('credit-payment');

    if (customerSelect.value && !creditRadio.checked) {
        // Auto-select credit payment when customer is selected
        creditRadio.checked = true;
        updatePaymentMethod();
    }
}

function processSale() {
    if (cart.length === 0) {
        showErrorNotification('Cart is empty');
        return;
    }

    const paymentMethod = document.querySelector('input[name="payment-method"]:checked').value;
    const customerType = document.querySelector('input[name="customer-type"]:checked').value;
    const customerId = paymentMethod === 'credit' ? document.getElementById('customer-select').value : null;
    const cashierId = document.getElementById('cashier-select').value;

    // Validate cashier selection
    if (!cashierId) {
        showErrorNotification('Please select a cashier');
        return;
    }

    if (paymentMethod === 'credit' && !customerId) {
        showErrorNotification('Please select a customer for credit payment');
        return;
    }

    // Disable process button to prevent double-clicks
    const processBtn = document.querySelector('.btn-success');
    const originalText = processBtn.innerHTML;
    processBtn.disabled = true;
    processBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

    fetch('/process_sale', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            cart_items: cart,
            payment_method: paymentMethod,
            customer_type: customerType,
            customer_id: customerId,
            cashier_id: cashierId
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // Store cart items before clearing for receipt
            const cartItemsForReceipt = [...cart];

            // Show receipt modal instead of just notification
            showReceiptModal(data, cartItemsForReceipt);

            // Clear cart and reset form
            clearCartAndReset();
        } else {
            // Handle different types of errors appropriately
            if (data.error_type === 'validation' || data.error_type === 'stock') {
                showErrorNotification(data.message);
                // Don't clear cart for validation errors - user can fix and retry
            } else {
                showErrorNotification(data.message);
                // For server errors, don't clear cart automatically
            }
        }
    })
    .catch(error => {
        console.error('Sale processing error:', error);

        if (error.message.includes('HTTP error')) {
            showErrorNotification('Sale failed. Please try again.');
        } else {
            showErrorNotification('Sale processing error. Please try again.');
        }
    })
    .finally(() => {
        // Re-enable button
        processBtn.disabled = false;
        processBtn.innerHTML = originalText;
    });
}

function clearCartAndReset() {
    // Clear cart
    cart = [];
    updateCartDisplay();

    // Refresh product stock
    refreshProductStock();

    // Reload credit summary
    loadCreditSummary();

    // Reset form to default state with null checks
    const cashRadio = document.querySelector('input[value="cash"]');
    const customerSelect = document.getElementById('customer-select');
    const cashReceivedInput = document.getElementById('cash-received');

    if (cashRadio) {
        cashRadio.checked = true;
    }

    if (customerSelect) {
        customerSelect.value = '';
    }

    if (cashReceivedInput) {
        cashReceivedInput.value = '';
    }

    updatePaymentMethod();
}

// Receipt Functions
function showReceiptModal(saleData, cartItems) {
    // Set transaction details
    document.getElementById('receipt-transaction-number').textContent = saleData.transaction_number;
    document.getElementById('receipt-payment-method').textContent = saleData.payment_method.charAt(0).toUpperCase() + saleData.payment_method.slice(1);
    document.getElementById('receipt-total').textContent = formatCurrency(saleData.total_amount);

    // Set current date and time
    const now = new Date();
    document.getElementById('receipt-date').textContent = now.toLocaleString();

    // Set cashier name
    const cashierSelect = document.getElementById('cashier-select');
    const cashierName = cashierSelect.options[cashierSelect.selectedIndex].text;
    document.getElementById('receipt-cashier-name').textContent = cashierName;

    // Show customer info for credit sales
    const customerRow = document.getElementById('receipt-customer-row');
    if (saleData.payment_method === 'credit' && saleData.customer_name) {
        document.getElementById('receipt-customer-name').textContent = saleData.customer_name;
        customerRow.style.display = 'block';
    } else {
        customerRow.style.display = 'none';
    }

    // Show cash details for cash sales
    const cashRow = document.getElementById('receipt-cash-row');
    const changeRow = document.getElementById('receipt-change-row');
    if (saleData.payment_method === 'cash') {
        const cashReceived = parseFloat(document.getElementById('cash-received').value) || saleData.total_amount;
        const change = cashReceived - saleData.total_amount;

        document.getElementById('receipt-cash-received').textContent = formatCurrency(cashReceived);
        document.getElementById('receipt-change').textContent = formatCurrency(change);

        cashRow.style.display = 'block';
        changeRow.style.display = 'block';
    } else {
        cashRow.style.display = 'none';
        changeRow.style.display = 'none';
    }

    // Build items list
    const itemsContainer = document.getElementById('receipt-items');
    itemsContainer.innerHTML = '';

    cartItems.forEach(item => {
        const itemDiv = document.createElement('div');
        itemDiv.className = 'row mb-1';
        itemDiv.innerHTML = `
            <div class="col-6">${item.name}</div>
            <div class="col-2 text-center">${item.quantity}</div>
            <div class="col-2 text-end">${formatCurrency(item.price)}</div>
            <div class="col-2 text-end">${formatCurrency(item.price * item.quantity)}</div>
        `;
        itemsContainer.appendChild(itemDiv);
    });

    // Add header row for items
    const headerDiv = document.createElement('div');
    headerDiv.className = 'row mb-2 border-bottom pb-1';
    headerDiv.innerHTML = `
        <div class="col-6"><strong>Item</strong></div>
        <div class="col-2 text-center"><strong>Qty</strong></div>
        <div class="col-2 text-end"><strong>Price</strong></div>
        <div class="col-2 text-end"><strong>Total</strong></div>
    `;
    itemsContainer.insertBefore(headerDiv, itemsContainer.firstChild);

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('receiptModal'));
    modal.show();
}

function printReceipt() {
    // Create a printable version of the receipt
    const receiptContent = document.querySelector('#receiptModal .modal-body').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>Receipt</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .row { display: flex; justify-content: space-between; margin-bottom: 5px; }
                .col-6 { flex: 0 0 50%; }
                .col-2 { flex: 0 0 16.66%; }
                .text-center { text-align: center; }
                .text-end { text-align: right; }
                .border-bottom { border-bottom: 1px solid #ccc; padding-bottom: 10px; }
                .border-top { border-top: 1px solid #ccc; padding-top: 10px; }
                .mb-1 { margin-bottom: 5px; }
                .mb-2 { margin-bottom: 10px; }
                .mb-3 { margin-bottom: 15px; }
                .pb-1 { padding-bottom: 5px; }
                .pb-2 { padding-bottom: 10px; }
                .pt-2 { padding-top: 10px; }
                h6 { margin: 10px 0 5px 0; }
                strong { font-weight: bold; }
            </style>
        </head>
        <body>
            ${receiptContent}
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

function startNewTransaction() {
    // This function is called when "New Transaction" button is clicked
    // The modal will already be dismissed by Bootstrap, so we just need to ensure everything is reset
    clearCartAndReset();
}

// Cashier Management Functions
function loadCashiers() {
    fetch('/api/cashiers')
        .then(response => response.json())
        .then(cashiers => {
            const select = document.getElementById('cashier-select');
            // Clear existing options except the first one
            select.innerHTML = '<option value="">Select Cashier</option>';

            cashiers.forEach(cashier => {
                const option = document.createElement('option');
                option.value = cashier.id;
                option.textContent = cashier.name;
                select.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading cashiers:', error);
            showErrorNotification('Failed to load cashiers');
        });
}

function saveCashier() {
    const nameInput = document.getElementById('cashier-name');
    const name = nameInput.value.trim();

    if (!name) {
        showErrorNotification('Please enter a cashier name');
        return;
    }

    fetch('/api/cashiers', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: name })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessNotification(`Cashier "${name}" added successfully`);

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addCashierModal'));
            modal.hide();

            // Clear form
            nameInput.value = '';

            // Reload cashiers and select the new one
            loadCashiers();
            setTimeout(() => {
                document.getElementById('cashier-select').value = data.cashier.id;
            }, 100);
        } else {
            showErrorNotification(data.error || 'Failed to add cashier');
        }
    })
    .catch(error => {
        console.error('Error adding cashier:', error);
        showErrorNotification('Failed to add cashier');
    });
}

// Backup Functions
function triggerFullBackup() {
    const backupBtn = document.getElementById('backup-btn');
    const originalText = backupBtn.innerHTML;

    // Show loading state
    backupBtn.disabled = true;
    backupBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Backing up...';

    fetch('/api/backup/full')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccessNotification(`Database backup completed! Saved to: ${data.backup_directory}`);
            } else {
                showErrorNotification(`Backup failed: ${data.error}`);
            }
        })
        .catch(error => {
            console.error('Backup error:', error);
            showErrorNotification('Backup failed. Please try again.');
        })
        .finally(() => {
            // Restore button state
            backupBtn.disabled = false;
            backupBtn.innerHTML = originalText;
        });
}

// Credit Management Functions
function loadCreditSummary() {
    fetch('/api/credit_summary')
        .then(response => response.json())
        .then(data => {
            document.getElementById('total-outstanding').textContent = formatCurrency(data.total_outstanding);
            document.getElementById('total-paid').textContent = formatCurrency(data.total_paid);
            document.getElementById('customers-with-credit').textContent = data.customers_with_credit;
        })
        .catch(error => {
            console.error('Error loading credit summary:', error);
        });
}

function updateProductStockDisplay(productId, newStock) {
    // Update desktop stock display
    const desktopStockBadge = document.querySelector(`[data-product-id="${productId}"] .stock-badge`);
    const desktopStockSpan = document.getElementById(`stock-${productId}`);

    // Update mobile stock display
    const mobileStockSpan = document.getElementById(`mobile-stock-${productId}`);

    if (desktopStockBadge) {
        desktopStockBadge.textContent = newStock;
        // Update stock badge class based on new stock level
        desktopStockBadge.className = 'stock-badge ' +
            (newStock === 0 ? 'stock-out' : newStock <= 5 ? 'stock-low' : 'stock-ok');
    }

    if (desktopStockSpan) {
        desktopStockSpan.textContent = newStock;
    }

    if (mobileStockSpan) {
        mobileStockSpan.textContent = newStock;
    }

    // Update data attributes
    const productItem = document.querySelector(`[data-product-id="${productId}"]`);
    if (productItem) {
        productItem.dataset.productStock = newStock;
    }

    // Disable/enable buttons based on stock
    const addButtons = document.querySelectorAll(`[data-product-id="${productId}"].add-to-cart`);
    const qtyInputs = document.querySelectorAll(`[data-product-id="${productId}"].qty-input`);
    const qtyButtons = document.querySelectorAll(`[data-product-id="${productId}"].qty-plus, [data-product-id="${productId}"].qty-minus`);

    addButtons.forEach(btn => {
        if (newStock === 0) {
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-times"></i> Out';
        } else {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-cart-plus"></i> Add';
        }
    });

    qtyInputs.forEach(input => {
        input.disabled = newStock === 0;
        input.max = newStock > 0 ? newStock : 1;
        if (parseInt(input.value) > newStock && newStock > 0) {
            input.value = newStock;
        }
    });

    qtyButtons.forEach(btn => {
        btn.disabled = newStock === 0;
    });
}

function refreshProductStock() {
    // Show refresh indicator
    const productCountBadge = document.getElementById('product-count');
    const originalText = productCountBadge.textContent;
    productCountBadge.innerHTML = '<i class="fas fa-sync fa-spin"></i> Updating...';

    // Fetch updated stock levels from server
    fetch('/api/product_stock')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                data.products.forEach(product => {
                    updateProductStockDisplay(product.id, product.stock);
                });

                // Update product count
                productCountBadge.textContent = `${data.products.length} products`;
            }
        })
        .catch(error => {
            console.error('Error refreshing stock:', error);
            // Restore original text on error
            productCountBadge.textContent = originalText;
        })
        .finally(() => {
            // Ensure the indicator is removed even if there's an error
            setTimeout(() => {
                if (productCountBadge.innerHTML.includes('fa-spin')) {
                    productCountBadge.textContent = originalText;
                }
            }, 1000);
        });
}

function updateCustomerInfo() {
    const customerSelect = document.getElementById('customer-select');
    const customerCreditInfo = document.getElementById('customer-credit-info');

    if (customerSelect.value) {
        const selectedOption = customerSelect.options[customerSelect.selectedIndex];
        const balance = parseFloat(selectedOption.dataset.balance || 0);
        const totalCredit = parseFloat(selectedOption.dataset.totalCredit || 0);
        const totalPaid = parseFloat(selectedOption.dataset.totalPaid || 0);

        document.getElementById('customer-total-credit').textContent = parseFloat(totalCredit).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});
        document.getElementById('customer-total-paid').textContent = parseFloat(totalPaid).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});
        document.getElementById('customer-balance').textContent = parseFloat(balance).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});

        customerCreditInfo.style.display = 'block';

        // Change alert class based on balance
        const alertDiv = customerCreditInfo.querySelector('.alert');
        alertDiv.className = 'alert p-2 ' + (balance > 0 ? 'alert-warning' : 'alert-info');
    } else {
        customerCreditInfo.style.display = 'none';
    }
}
</script>

<style>
/* Search and Filter Styles */
.search-container {
    position: relative;
    width: 100%;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    height: 38px;
    box-sizing: border-box;
}

.search-input-wrapper:focus-within {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 14px;
    z-index: 2;
    pointer-events: none;
}

.search-input {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    background: transparent;
    padding: 0 40px 0 35px;
    width: 100%;
    border-radius: 0.375rem;
    font-size: 1rem;
    height: 36px;
    line-height: 1.5;
    display: flex;
    align-items: center;
}

.search-input:focus {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
}

.clear-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    font-size: 14px;
    padding: 4px;
    cursor: pointer;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    transition: all 0.2s ease;
}

.clear-btn:hover {
    background-color: #f8f9fa;
    color: #495057;
}

mark {
    background-color: #fff3cd;
    padding: 1px 2px;
    border-radius: 2px;
    font-weight: bold;
}

/* Compact Product Card Styles */
.compact-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.2s ease;
    height: 180px; /* Fixed height for consistency */
}

.compact-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-color: #0d6efd;
}

.compact-card .card-body {
    padding: 8px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.product-name {
    font-size: 0.9rem;
    font-weight: 600;
    color: #333;
    line-height: 1.2;
    height: 2.4em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.price-stock-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 4px 0;
}

.price-tag {
    font-weight: bold;
    color: #198754;
    font-size: 1rem;
}

.stock-badge {
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 12px;
    font-weight: 600;
    min-width: 24px;
    text-align: center;
}

.stock-ok {
    background-color: #d1edff;
    color: #0969da;
}

.stock-low {
    background-color: #fff3cd;
    color: #856404;
}

.stock-out {
    background-color: #f8d7da;
    color: #721c24;
}

.quantity-controls-compact {
    margin: 4px 0;
}

.qty-row {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;
    margin-bottom: 4px;
}

.qty-input-compact {
    width: 50px;
    height: 28px;
    font-size: 0.85rem;
    font-weight: bold;
    text-align: center;
    padding: 2px 4px;
}

.qty-row .btn {
    width: 28px;
    height: 28px;
    padding: 0;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quick-qty-compact {
    display: flex;
    gap: 2px;
    justify-content: center;
}

.btn-xs {
    padding: 1px 4px;
    font-size: 0.7rem;
    line-height: 1;
    border-radius: 3px;
    min-width: 20px;
    height: 20px;
}

.compact-btn {
    font-size: 0.8rem;
    padding: 4px 8px;
    height: 28px;
    border-radius: 4px;
}

/* Grid Layout Optimization */
@media (min-width: 1400px) {
    .col-xl-3 {
        flex: 0 0 20%; /* 5 columns on extra large screens */
        max-width: 20%;
    }
}

@media (min-width: 1200px) and (max-width: 1399px) {
    .col-xl-3 {
        flex: 0 0 25%; /* 4 columns on large screens */
        max-width: 25%;
    }
}

/* Original Product Card Styles */
.product-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #0d6efd;
}

.cart-item {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 8px;
    margin-bottom: 6px;
    transition: all 0.2s ease;
}

.cart-item:hover {
    box-shadow: 0 2px 6px rgba(0,0,0,0.08);
    border-color: #0d6efd;
}

.quantity-controls {
    max-width: 120px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.quantity-controls .input-group {
    margin: 0 auto;
    justify-content: center;
}

.qty-input {
    font-weight: bold;
    text-align: center;
}

.quick-qty {
    display: flex;
    gap: 4px;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.quick-qty-btn {
    min-width: 30px;
    padding: 2px 6px;
    font-size: 0.75rem;
    border-radius: 4px;
}

.cart-summary {
    border-top: 2px solid #0d6efd;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.cart-total {
    font-size: 1.25rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

/* Mobile compact product layout */
.mobile-products-grid {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.mobile-product-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.mobile-product-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-color: #0d6efd;
}

.mobile-product-info {
    flex: 1;
    min-width: 0;
}

.mobile-product-name {
    font-weight: 600;
    font-size: 0.95rem;
    color: #333;
    margin-bottom: 2px;
}

.mobile-product-price {
    font-weight: bold;
    color: #198754;
    font-size: 1rem;
    margin-bottom: 2px;
}

.mobile-product-stock {
    font-size: 0.8rem;
    color: #6c757d;
}

.mobile-product-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

.mobile-qty-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.mobile-qty-input {
    width: 50px;
    height: 36px;
    font-size: 0.9rem;
    font-weight: bold;
}

.mobile-add-btn {
    min-width: 44px;
    height: 36px;
    padding: 0 8px;
    font-size: 0.9rem;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    /* Search and filter on mobile */
    .row.mb-3 {
        margin-bottom: 1rem !important;
    }

    .row.mb-3 .col-md-8,
    .row.mb-3 .col-md-4 {
        margin-bottom: 0.5rem;
    }

    #product-search {
        font-size: 16px; /* Prevent zoom on iOS */
    }

    /* Compact cards on mobile */
    .compact-card {
        height: 160px;
    }

    .product-name {
        font-size: 0.85rem;
        height: 2.2em;
    }

    .price-tag {
        font-size: 0.9rem;
    }

    .stock-badge {
        font-size: 0.7rem;
        padding: 1px 4px;
    }

    .product-card .card-body {
        padding: 1.5rem 1rem;
    }

    .quantity-controls {
        max-width: 140px;
    }

    .cart-item {
        padding: 8px;
        margin-bottom: 6px;
    }

    /* Compact shopping cart for mobile */
    .shopping-cart .form-label {
        margin-bottom: 0.25rem;
        font-size: 0.875rem;
    }

    .shopping-cart .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .btn-group-sm .btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
        min-height: 44px; /* Touch-friendly size */
    }

    .add-to-cart {
        padding: 0.75rem 1rem;
        font-size: 1rem;
        min-height: 48px;
    }

    .qty-minus, .qty-plus {
        min-width: 44px;
        min-height: 44px;
    }

    .qty-input {
        min-height: 44px;
        font-size: 1.1rem;
    }

    .quick-qty-btn {
        min-width: 44px;
        min-height: 36px;
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .col-md-8, .col-md-4 {
        padding: 0.5rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .cart-total {
        font-size: 1.3rem;
        padding: 1.5rem;
    }

    .btn-lg {
        padding: 1rem 2rem;
        font-size: 1.2rem;
        min-height: 56px;
    }

    .form-select, .form-control {
        min-height: 48px;
        font-size: 1.1rem;
    }

    .btn-group .btn {
        min-height: 44px;
        padding: 0.5rem 1rem;
    }

    /* Make product grid single column on very small screens */
    .col-md-6.col-lg-4 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* Loading states */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Mobile Floating Action Button */
.mobile-cart-fab {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.mobile-cart-fab .btn {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    position: relative;
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    min-width: 20px;
    height: 20px;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart-count.hidden {
    display: none;
}

/* Mobile offcanvas enhancements */
.offcanvas-bottom {
    height: 70vh;
}

.offcanvas-body {
    padding: 1.5rem;
}
</style>
{% endblock %}

