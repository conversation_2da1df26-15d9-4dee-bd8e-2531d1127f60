#!/usr/bin/env python3
"""
Import transactions from the provided data
Generates transaction IDs, updates stock, creates all necessary database records
"""

from app import app, db, Product, Transaction, TransactionItem, Sale, Cashier, Customer, CreditTransaction
from datetime import datetime, timezone, timedelta
import random

def import_transactions():
    """Import the provided transaction data"""
    with app.app_context():
        print("📥 Importing Transaction Data...")
        print("=" * 60)
        
        # Transaction data from user with proper date format and payment methods
        transaction_data = [
            # June 17, 2025
            {"qty": 1, "product": "Chicken Nuggets", "price": 60, "cashier": "m", "date": "2025-06-17", "payment": "cash"},
            {"qty": 1, "product": "Chicken Teriyaki", "price": 80, "cashier": "m", "date": "2025-06-17", "payment": "cash"},
            {"qty": 1, "product": "Pork Longanisa", "price": 65, "cashier": "m", "date": "2025-06-17", "payment": "cash"},

            # June 18, 2025
            {"qty": 1, "product": "Pork Teriyaki", "price": 75, "cashier": "a", "date": "2025-06-18", "payment": "cash"},
            {"qty": 1, "product": "Pork Tocino", "price": 75, "cashier": "n", "date": "2025-06-18", "payment": "cash"},
            {"qty": 2, "product": "Skinless Longanisa", "price": 130, "cashier": "a", "date": "2025-06-18", "payment": "cash"},
            {"qty": 2, "product": "Chicken Nuggets", "price": 120, "cashier": "g", "date": "2025-06-18", "payment": "cash"},
            {"qty": 2, "product": "Skinless Longanisa", "price": 130, "cashier": "g", "date": "2025-06-18", "payment": "cash"},
            {"qty": 2, "product": "Pork Longanisa", "price": 130, "cashier": "g", "date": "2025-06-18", "payment": "cash"},
            {"qty": 2, "product": "Pork Teriyaki", "price": 150, "cashier": "g", "date": "2025-06-18", "payment": "cash"},
            {"qty": 1, "product": "Pork Sisig", "price": 110, "cashier": "y", "date": "2025-06-18", "payment": "cash"},
            {"qty": 1, "product": "Pork Tocino", "price": 75, "cashier": "g", "date": "2025-06-18", "payment": "cash"},

            # June 19, 2025
            {"qty": 1, "product": "Pork Sisig", "price": 110, "cashier": "a", "date": "2025-06-19", "payment": "cash"},

            # June 20, 2025
            {"qty": 1, "product": "Big Siomai", "price": 70, "cashier": "a", "date": "2025-06-20", "payment": "cash"},

            # June 22, 2025
            {"qty": 1, "product": "Pork Dinakdakan", "price": 110, "cashier": "h", "date": "2025-06-22", "payment": "cash"},
            {"qty": 1, "product": "Pork Teriyaki", "price": 75, "cashier": "h", "date": "2025-06-22", "payment": "cash"},
            {"qty": 1, "product": "Pork Sisig", "price": 110, "cashier": "a", "date": "2025-06-22", "payment": "cash"},
            {"qty": 2, "product": "Pork Tapa", "price": 150, "cashier": "a", "date": "2025-06-22", "payment": "cash"},

            # June 23, 2025
            {"qty": 1, "product": "Pork Dinakdakan", "price": 110, "cashier": "y", "date": "2025-06-23", "payment": "cash"},
            {"qty": 2, "product": "Chicken Nuggets", "price": 120, "cashier": "y", "date": "2025-06-23", "payment": "cash"},
            {"qty": 1, "product": "Chicken Pastil", "price": 110, "cashier": "y", "date": "2025-06-23", "payment": "cash"},
            {"qty": 1, "product": "Pork Longadog", "price": 65, "cashier": "h", "date": "2025-06-23", "payment": "cash"},

            # June 28, 2025
            {"qty": 1, "product": "Salami w/ Cheese", "price": 90, "cashier": "m", "date": "2025-06-28", "payment": "cash"},
            {"qty": 2, "product": "Chicken Teriyaki", "price": 160, "cashier": "m", "date": "2025-06-28", "payment": "cash"},
            {"qty": 1, "product": "Pork Sisig", "price": 110, "cashier": "m", "date": "2025-06-28", "payment": "cash"},
            {"qty": 1, "product": "Pork Tapa", "price": 75, "cashier": "h", "date": "2025-06-28", "payment": "cash"},
            {"qty": 1, "product": "Pork Sisig", "price": 110, "cashier": "y", "date": "2025-06-28", "payment": "cash"},

            # June 29, 2025
            {"qty": 1, "product": "Pork Sisig", "price": 110, "cashier": "y", "date": "2025-06-29", "payment": "cash"},

            # June 30, 2025
            {"qty": 1, "product": "Salami w/ Cheese", "price": 90, "cashier": "h", "date": "2025-06-30", "payment": "cash"},
            {"qty": 1, "product": "Pork Sisig", "price": 110, "cashier": "n", "date": "2025-06-30", "payment": "cash"},

            # July 2, 2025
            {"qty": 2, "product": "Chicken Pastil", "price": 220, "cashier": "y", "date": "2025-07-02", "payment": "cash"},

            # July 3, 2025
            {"qty": 1, "product": "Pork Tapa", "price": 75, "cashier": "a", "date": "2025-07-03", "payment": "cash"},

            # July 4, 2025
            {"qty": 1, "product": "Cheesy Hamonado", "price": 90, "cashier": "a", "date": "2025-07-04", "payment": "cash"},
            {"qty": 1, "product": "Pork Tapa", "price": 75, "cashier": "a", "date": "2025-07-04", "payment": "cash"},
            {"qty": 1, "product": "Chicken Tocino", "price": 80, "cashier": "a", "date": "2025-07-04", "payment": "cash"},
            {"qty": 2, "product": "Pork Longanisa", "price": 130, "cashier": "n", "date": "2025-07-04", "payment": "cash"},

            # July 5, 2025
            {"qty": 1, "product": "Pork Teriyaki", "price": 75, "cashier": "a", "date": "2025-07-05", "payment": "cash"},

            # July 6, 2025
            {"qty": 1, "product": "Big Siomai", "price": 70, "cashier": "y", "date": "2025-07-06", "payment": "cash"},
            {"qty": 1, "product": "Pork Meatballs", "price": 60, "cashier": "y", "date": "2025-07-06", "payment": "cash"},
            {"qty": 1, "product": "Pork Sisig", "price": 110, "cashier": "y", "date": "2025-07-06", "payment": "cash"},
            {"qty": 1, "product": "Chicken Teriyaki", "price": 80, "cashier": "y", "date": "2025-07-06", "payment": "cash"},
            {"qty": 1, "product": "Pork Teriyaki", "price": 75, "cashier": "y", "date": "2025-07-06", "payment": "cash"},
            {"qty": 1, "product": "Cheesy Hamonado", "price": 90, "cashier": "y", "date": "2025-07-06", "payment": "cash"},
            {"qty": 1, "product": "Pork Meatballs", "price": 60, "cashier": "c", "date": "2025-07-06", "payment": "cash"},

            # July 7, 2025
            {"qty": 1, "product": "Sliced Ham", "price": 75, "cashier": "a", "date": "2025-07-07", "payment": "cash"},

            # July 8, 2025
            {"qty": 1, "product": "Chicken Nuggets", "price": 60, "cashier": "c", "date": "2025-07-08", "payment": "cash"},

            # July 9, 2025 - Credit transactions
            {"qty": 1, "product": "Chicken Pastil", "price": 110, "cashier": "a", "date": "2025-07-09", "payment": "credit", "customer": "Chelsea"},
            {"qty": 1, "product": "Pork Longadog", "price": 65, "cashier": "a", "date": "2025-07-09", "payment": "credit", "customer": "Ate Joy"},
            {"qty": 1, "product": "Pork Longanisa", "price": 65, "cashier": "a", "date": "2025-07-09", "payment": "credit", "customer": "Chelsea"},
            {"qty": 1, "product": "Chicken Pastil", "price": 110, "cashier": "a", "date": "2025-07-09", "payment": "credit", "customer": "Ate Joy"},

            # July 12, 2025
            {"qty": 2, "product": "Pork Sisig", "price": 220, "cashier": "y", "date": "2025-07-12", "payment": "cash"},

            # July 13, 2025
            {"qty": 2, "product": "Big Siomai", "price": 140, "cashier": "c", "date": "2025-07-13", "payment": "cash"},
            {"qty": 1, "product": "Big Siomai", "price": 70, "cashier": "g", "date": "2025-07-13", "payment": "cash"},
            {"qty": 1, "product": "Pork Teriyaki", "price": 75, "cashier": "a", "date": "2025-07-13", "payment": "cash"}
        ]
        
        # Cashier mapping
        cashier_mapping = {
            "a": "Abbie",
            "m": "Mama",
            "h": "House",
            "n": "Nonoy",
            "g": "Gian",
            "y": "Yasha",
            "c": "Caleb"
        }
        
        print("👥 Cashier Mapping:")
        for code, name in cashier_mapping.items():
            print(f"   {code} → {name}")
        
        # Get cashiers from database
        cashiers = {cashier.name: cashier for cashier in Cashier.query.all()}
        print(f"\n🗄️ Found {len(cashiers)} cashiers in database")
        
        # Get products from database
        products = {product.name: product for product in Product.query.all()}
        print(f"📦 Found {len(products)} products in database")

        # Get customers from database
        customers = {customer.name: customer for customer in Customer.query.all()}
        print(f"👥 Found {len(customers)} customers in database")
        
        print(f"\n💰 Processing {len(transaction_data)} items as individual transactions...")

        # Find the highest existing transaction number to avoid conflicts
        existing_transactions = Transaction.query.all()
        max_counter = 2000  # Default starting point

        for txn in existing_transactions:
            if txn.transaction_number.startswith('TXN'):
                try:
                    # Extract number from TXN20250716002001 format
                    number_part = txn.transaction_number[-6:]  # Last 6 digits
                    counter = int(number_part)
                    max_counter = max(max_counter, counter)
                except:
                    continue

        transaction_counter = max_counter  # Start from highest existing number
        total_transactions_created = 0
        total_revenue = 0

        print(f"🔢 Starting transaction counter from: {transaction_counter + 1}")

        # Process each item as a separate transaction
        for i, item in enumerate(transaction_data):
            cashier_name = cashier_mapping[item["cashier"]]

            if cashier_name not in cashiers:
                print(f"   ❌ Cashier '{cashier_name}' not found in database!")
                continue

            cashier = cashiers[cashier_name]
            print(f"\n👤 Creating transaction {i+1}/{len(transaction_data)} for {cashier_name}...")

            # Each item becomes one transaction
            transaction_counter += 1

            # Generate transaction number
            transaction_number = f"TXN{datetime.now().strftime('%Y%m%d')}{transaction_counter:06d}"

            # Use the specific date from the transaction data
            if 'date' in item and item['date']:
                # Parse the date string (YYYY-MM-DD format)
                date_parts = item['date'].split('-')
                year, month, day = int(date_parts[0]), int(date_parts[1]), int(date_parts[2])

                # Add random time within business hours (8 AM to 8 PM)
                hour = random.randint(8, 20)
                minute = random.randint(0, 59)
                second = random.randint(0, 59)

                transaction_date = datetime(year, month, day, hour, minute, second, tzinfo=timezone.utc)
                print(f"   📅 Using specified date: {item['date']} at {hour:02d}:{minute:02d}")
            else:
                # Fallback to random date within last 7 days
                days_ago = random.randint(0, 7)
                hours_ago = random.randint(0, 23)
                minutes_ago = random.randint(0, 59)
                transaction_date = datetime.now(timezone.utc) - timedelta(
                    days=days_ago, hours=hours_ago, minutes=minutes_ago
                )
                print(f"   📅 Using random date: {transaction_date.strftime('%Y-%m-%d %H:%M')}")

            # Calculate total amount (single item)
            total_amount = item["price"]

            # Determine payment method
            payment_method = item.get("payment", "cash")
            customer_name = item.get("customer", None)

            # Only add to revenue if it's a cash transaction
            if payment_method == "cash":
                total_revenue += total_amount

            # Create transaction
            transaction = Transaction(
                transaction_number=transaction_number,
                total_amount=total_amount,
                cashier_id=cashier.id,
                transaction_date=transaction_date,
                payment_method=payment_method
            )
            db.session.add(transaction)
            db.session.flush()  # Get transaction ID

            payment_info = f"({payment_method.upper()}"
            if customer_name:
                payment_info += f" - {customer_name}"
            payment_info += ")"

            print(f"   📋 Transaction {transaction_number}: ₱{total_amount:.2f} {payment_info}")

            # Find product
            product = products.get(item["product"])
            if not product:
                print(f"      ❌ Product '{item['product']}' not found!")
                continue

            # Verify price matches
            expected_total = product.price * item["qty"]
            if abs(expected_total - item["price"]) > 0.01:
                print(f"      ⚠️  Price mismatch for {item['product']}: expected ₱{expected_total:.2f}, got ₱{item['price']:.2f}")

            # Create transaction item
            transaction_item = TransactionItem(
                transaction_id=transaction.id,
                product_id=product.id,
                quantity=item["qty"],
                unit_price=product.price,
                total_price=item["price"]
            )
            db.session.add(transaction_item)

            # Create sale record
            sale = Sale(
                product_id=product.id,
                transaction_id=transaction.id,
                quantity=item["qty"],
                unit_price=product.price,
                total_price=item["price"],
                sale_date=transaction_date
            )
            db.session.add(sale)

            # Handle credit transactions - update customer records
            if payment_method == "credit" and customer_name:
                # Find or create customer
                customer = customers.get(customer_name)
                if not customer:
                    print(f"      ❌ Customer '{customer_name}' not found in database!")
                    print(f"      💡 Creating new customer: {customer_name}")
                    customer = Customer(name=customer_name)
                    db.session.add(customer)
                    db.session.flush()  # Get customer ID
                    customers[customer_name] = customer

                # Update customer credit
                customer.total_credit += total_amount
                print(f"      💳 Added ₱{total_amount:.2f} credit to {customer_name} (Total: ₱{customer.total_credit:.2f})")

                # Create credit transaction record
                credit_transaction = CreditTransaction(
                    customer_id=customer.id,
                    transaction_type='credit',
                    amount=total_amount,
                    description=f"Credit sale: {item['qty']}x {item['product']}",
                    created_at=transaction_date
                )
                db.session.add(credit_transaction)

            # Update product stock and sold count
            if product.stock >= item["qty"]:
                product.stock -= item["qty"]
                product.sold += item["qty"]
                print(f"      ✅ {item['qty']}x {item['product']} @ ₱{product.price:.2f} (Stock: {product.stock})")
            else:
                print(f"      ⚠️  Insufficient stock for {item['product']}: need {item['qty']}, have {product.stock}")
                # Still process the sale but warn about negative stock
                product.stock -= item["qty"]
                product.sold += item["qty"]

            total_transactions_created += 1
        
        # Commit all changes
        db.session.commit()
        
        print("\n" + "=" * 60)
        print("🎉 Transaction Import Complete!")
        print("=" * 60)
        
        # Calculate summary statistics
        cash_transactions = [item for item in transaction_data if item.get("payment", "cash") == "cash"]
        credit_transactions = [item for item in transaction_data if item.get("payment", "cash") == "credit"]
        total_credit_amount = sum(item["price"] for item in credit_transactions)

        print(f"📊 IMPORT SUMMARY:")
        print(f"   💰 Total Transactions Created: {total_transactions_created}")
        print(f"   💵 Cash Revenue Generated: ₱{total_revenue:.2f}")
        print(f"   💳 Credit Sales Created: ₱{total_credit_amount:.2f}")
        print(f"   📦 Total Items Processed: {len(transaction_data)}")
        print(f"   💸 Cash Transactions: {len(cash_transactions)}")
        print(f"   🏪 Credit Transactions: {len(credit_transactions)}")

        print(f"\n👥 CASHIER BREAKDOWN:")
        # Group by cashier for summary
        cashier_summary = {}
        for item in transaction_data:
            cashier_name = cashier_mapping[item["cashier"]]
            payment_method = item.get("payment", "cash")
            if cashier_name not in cashier_summary:
                cashier_summary[cashier_name] = {"cash_items": 0, "credit_items": 0, "cash_revenue": 0, "credit_sales": 0}

            if payment_method == "cash":
                cashier_summary[cashier_name]["cash_items"] += 1
                cashier_summary[cashier_name]["cash_revenue"] += item["price"]
            else:
                cashier_summary[cashier_name]["credit_items"] += 1
                cashier_summary[cashier_name]["credit_sales"] += item["price"]

        for cashier_name, summary in cashier_summary.items():
            if cashier_name in cashiers:
                total_items = summary["cash_items"] + summary["credit_items"]
                total_amount = summary["cash_revenue"] + summary["credit_sales"]
                print(f"   {cashier_name}: {total_items} transactions (Cash: {summary['cash_items']}, Credit: {summary['credit_items']}), ₱{total_amount:.2f} total")
        
        print(f"\n📈 PRODUCT SALES:")
        product_sales = {}
        for item in transaction_data:
            product_name = item["product"]
            if product_name not in product_sales:
                product_sales[product_name] = {"qty": 0, "revenue": 0}
            product_sales[product_name]["qty"] += item["qty"]
            product_sales[product_name]["revenue"] += item["price"]
        
        for product_name, sales in sorted(product_sales.items(), key=lambda x: x[1]["revenue"], reverse=True):
            print(f"   {product_name}: {sales['qty']} units, ₱{sales['revenue']:.2f}")

        # Show credit customer information
        if credit_transactions:
            print(f"\n💳 CREDIT CUSTOMERS:")
            credit_customers = {}
            for item in credit_transactions:
                customer_name = item.get("customer", "Unknown")
                if customer_name not in credit_customers:
                    credit_customers[customer_name] = {"amount": 0, "items": 0}
                credit_customers[customer_name]["amount"] += item["price"]
                credit_customers[customer_name]["items"] += 1

            for customer_name, info in credit_customers.items():
                print(f"   {customer_name}: ₱{info['amount']:.2f} credit ({info['items']} items) - NOT PAID")

        print(f"\n🔄 CSV BACKUP:")
        print("   Automatic CSV backup will be triggered for these transactions")

        print(f"\n💡 NEXT STEPS:")
        print("   1. Visit Dashboard to see updated revenue (cash only)")
        print("   2. Check Credit Management to see Bets's debt")
        print("   3. Visit Cashier Performance page to see updated metrics")
        print("   4. Check Reports page for sales analysis")
        print("   5. Verify stock levels in Product Management")
        print("   6. Review CSV backups in backups/ folder")

if __name__ == "__main__":
    import_transactions()
