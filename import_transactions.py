#!/usr/bin/env python3
"""
Import transactions from the provided data
Generates transaction IDs, updates stock, creates all necessary database records
"""

from app import app, db, Product, Transaction, TransactionItem, Sale, Cashier
from datetime import datetime, timezone, timedelta
import random

def import_transactions():
    """Import the provided transaction data"""
    with app.app_context():
        print("📥 Importing Transaction Data...")
        print("=" * 60)
        
        # Transaction data from user
        transaction_data = [
            {"qty": 1, "product": "Chicken Nuggets", "price": 60, "cashier": "m"}, june 17, 2025
            {"qty": 1, "product": "Chicken Teriyaki", "price": 80, "cashier": "m"},june 17, 2025
            {"qty": 1, "product": "Pork Longanisa", "price": 65, "cashier": "m"},june 17, 2025
            {"qty": 1, "product": "Pork Teriyaki", "price": 75, "cashier": "a"},june 18, 2025
            {"qty": 1, "product": "Pork Tocino", "price": 75, "cashier": "n"},june 18, 2025
            {"qty": 2, "product": "Skinless Longanisa", "price": 130, "cashier": "a"},june 18, 2025
            {"qty": 2, "product": "Chicken Nuggets", "price": 120, "cashier": "g"},june 18, 2025
            {"qty": 2, "product": "Skinless Longanisa", "price": 130, "cashier": "g"},june 18, 2025
            {"qty": 2, "product": "Pork Longanisa", "price": 130, "cashier": "g"},june 18, 2025
            {"qty": 2, "product": "Pork Teriyaki", "price": 150, "cashier": "g"},june 18, 2025
            {"qty": 1, "product": "Pork Sisig", "price": 110, "cashier": "y"},june 18, 2025
            {"qty": 1, "product": "Pork Tocino", "price": 75, "cashier": "g"},june 18, 2025
            {"qty": 1, "product": "Pork Sisig", "price": 110, "cashier": "a"},  june 19, 2025
            {"qty": 1, "product": "Big Siomai", "price": 70, "cashier": "a"},   june 20, 2025
            {"qty": 1, "product": "Pork Dinakdakan", "price": 110, "cashier": "h"},  june 22, 2025
            {"qty": 1, "product": "Pork Teriyaki", "price": 75, "cashier": "h"},june 22, 2025
            {"qty": 1, "product": "Pork Sisig", "price": 110, "cashier": "a"},  june 22, 2025
            {"qty": 2, "product": "Pork Tapa", "price": 150, "cashier": "a"}june 22, 2025
        ]
        
        # Cashier mapping
        cashier_mapping = {
            "a": "Abbie",
            "m": "Mama", 
            "h": "House",  
            "n": "Nonoy",
            "g": "Gian",
            "y": "Yasha",
            "h": "House"
        }
        
        print("👥 Cashier Mapping:")
        for code, name in cashier_mapping.items():
            print(f"   {code} → {name}")
        
        # Get cashiers from database
        cashiers = {cashier.name: cashier for cashier in Cashier.query.all()}
        print(f"\n🗄️ Found {len(cashiers)} cashiers in database")
        
        # Get products from database
        products = {product.name: product for product in Product.query.all()}
        print(f"📦 Found {len(products)} products in database")
        
        print(f"\n💰 Processing {len(transaction_data)} items as individual transactions...")

        # Find the highest existing transaction number to avoid conflicts
        existing_transactions = Transaction.query.all()
        max_counter = 2000  # Default starting point

        for txn in existing_transactions:
            if txn.transaction_number.startswith('TXN'):
                try:
                    # Extract number from TXN20250716002001 format
                    number_part = txn.transaction_number[-6:]  # Last 6 digits
                    counter = int(number_part)
                    max_counter = max(max_counter, counter)
                except:
                    continue

        transaction_counter = max_counter  # Start from highest existing number
        total_transactions_created = 0
        total_revenue = 0

        print(f"🔢 Starting transaction counter from: {transaction_counter + 1}")

        # Process each item as a separate transaction
        for i, item in enumerate(transaction_data):
            cashier_name = cashier_mapping[item["cashier"]]

            if cashier_name not in cashiers:
                print(f"   ❌ Cashier '{cashier_name}' not found in database!")
                continue

            cashier = cashiers[cashier_name]
            print(f"\n👤 Creating transaction {i+1}/{len(transaction_data)} for {cashier_name}...")

            # Each item becomes one transaction
            transaction_counter += 1

            # Generate transaction number
            transaction_number = f"TXN{datetime.now().strftime('%Y%m%d')}{transaction_counter:06d}"

            # Random date within last 7 days (you can modify this section)
            days_ago = random.randint(0, 7)
            hours_ago = random.randint(0, 23)
            minutes_ago = random.randint(0, 59)
            transaction_date = datetime.now(timezone.utc) - timedelta(
                days=days_ago, hours=hours_ago, minutes=minutes_ago
            )

            # OPTION: Set specific date for historical data
            # Uncomment and modify the line below to set a specific date:
            # transaction_date = datetime(2025, 7, 10, 14, 30, 0, tzinfo=timezone.utc)  # Example: July 10, 2025 at 2:30 PM

            # Calculate total amount (single item)
            total_amount = item["price"]
            total_revenue += total_amount

            # Create transaction (all imported transactions are cash)
            transaction = Transaction(
                transaction_number=transaction_number,
                total_amount=total_amount,
                cashier_id=cashier.id,
                transaction_date=transaction_date,
                payment_method='cash'
            )
            db.session.add(transaction)
            db.session.flush()  # Get transaction ID

            print(f"   📋 Transaction {transaction_number}: ₱{total_amount:.2f} (1 item)")

            # Find product
            product = products.get(item["product"])
            if not product:
                print(f"      ❌ Product '{item['product']}' not found!")
                continue

            # Verify price matches
            expected_total = product.price * item["qty"]
            if abs(expected_total - item["price"]) > 0.01:
                print(f"      ⚠️  Price mismatch for {item['product']}: expected ₱{expected_total:.2f}, got ₱{item['price']:.2f}")

            # Create transaction item
            transaction_item = TransactionItem(
                transaction_id=transaction.id,
                product_id=product.id,
                quantity=item["qty"],
                unit_price=product.price,
                total_price=item["price"]
            )
            db.session.add(transaction_item)

            # Create sale record
            sale = Sale(
                product_id=product.id,
                transaction_id=transaction.id,
                quantity=item["qty"],
                unit_price=product.price,
                total_price=item["price"],
                sale_date=transaction_date
            )
            db.session.add(sale)

            # Update product stock and sold count
            if product.stock >= item["qty"]:
                product.stock -= item["qty"]
                product.sold += item["qty"]
                print(f"      ✅ {item['qty']}x {item['product']} @ ₱{product.price:.2f} (Stock: {product.stock})")
            else:
                print(f"      ⚠️  Insufficient stock for {item['product']}: need {item['qty']}, have {product.stock}")
                # Still process the sale but warn about negative stock
                product.stock -= item["qty"]
                product.sold += item["qty"]

            total_transactions_created += 1
        
        # Commit all changes
        db.session.commit()
        
        print("\n" + "=" * 60)
        print("🎉 Transaction Import Complete!")
        print("=" * 60)
        
        print(f"📊 IMPORT SUMMARY:")
        print(f"   💰 Total Transactions Created: {total_transactions_created}")
        print(f"   💵 Total Revenue Generated: ₱{total_revenue:.2f}")
        print(f"   📦 Total Items Processed: {len(transaction_data)}")
        
        print(f"\n👥 CASHIER BREAKDOWN:")
        # Group by cashier for summary
        cashier_summary = {}
        for item in transaction_data:
            cashier_name = cashier_mapping[item["cashier"]]
            if cashier_name not in cashier_summary:
                cashier_summary[cashier_name] = {"items": 0, "revenue": 0}
            cashier_summary[cashier_name]["items"] += 1
            cashier_summary[cashier_name]["revenue"] += item["price"]

        for cashier_name, summary in cashier_summary.items():
            if cashier_name in cashiers:
                print(f"   {cashier_name}: {summary['items']} transactions, ₱{summary['revenue']:.2f} revenue")
        
        print(f"\n📈 PRODUCT SALES:")
        product_sales = {}
        for item in transaction_data:
            product_name = item["product"]
            if product_name not in product_sales:
                product_sales[product_name] = {"qty": 0, "revenue": 0}
            product_sales[product_name]["qty"] += item["qty"]
            product_sales[product_name]["revenue"] += item["price"]
        
        for product_name, sales in sorted(product_sales.items(), key=lambda x: x[1]["revenue"], reverse=True):
            print(f"   {product_name}: {sales['qty']} units, ₱{sales['revenue']:.2f}")
        
        print(f"\n🔄 CSV BACKUP:")
        print("   Automatic CSV backup will be triggered for these transactions")
        
        print(f"\n💡 NEXT STEPS:")
        print("   1. Visit Cashier Performance page to see updated metrics")
        print("   2. Check Reports page for sales analysis")
        print("   3. Verify stock levels in Product Management")
        print("   4. Review CSV backups in backups/ folder")

if __name__ == "__main__":
    import_transactions()
