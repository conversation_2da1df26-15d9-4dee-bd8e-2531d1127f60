@echo off
echo 🏪 ATE MEG's <PERSON><PERSON><PERSON><PERSON> FOODS POS System Launcher
echo ===============================================

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed or not in PATH
    echo 💡 Please install Python from https://python.org
    echo 💡 Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo ✅ Python found
echo 🔄 Starting application...
echo.

REM Start the application
python start_app.py

echo.
echo 🛑 Application stopped
pause
