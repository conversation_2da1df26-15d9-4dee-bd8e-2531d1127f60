@echo off
echo 🗑️ Removing Virtual Environment (venv) folder...
echo This will make the system work on any device without specific Python paths
echo.

REM Check if venv folder exists
if exist "venv" (
    echo ✅ Found venv folder - removing it...
    
    REM Remove read-only attributes recursively
    attrib -r "venv\*.*" /s
    
    REM Remove the folder
    rmdir /s /q "venv"
    
    if exist "venv" (
        echo ❌ Could not remove venv folder completely
        echo 💡 Please manually delete the 'venv' folder
    ) else (
        echo ✅ venv folder removed successfully!
    )
) else (
    echo ✅ venv folder not found - already clean!
)

echo.
echo 🎉 System is now universal and will work on any device!
echo 💡 The system will use the global Python installation instead
echo 💡 This makes it portable and device-independent
echo.
pause
