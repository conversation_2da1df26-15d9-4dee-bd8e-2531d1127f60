#!/usr/bin/env python3
"""
Deployment script for ATE MEG's FROZEN FOODS POS System
This script prepares the application for production deployment
"""

import os
import shutil
from app import app, db
from init_data import init_sample_data

def prepare_for_deployment():
    """Prepare the application for deployment"""
    
    print("🚀 Preparing ATE MEG's FROZEN FOODS POS System for deployment...")
    
    # Ensure database exists and is initialized
    with app.app_context():
        # Check if database file exists
        db_path = 'instance/frozen_foods_pos.db'
        
        if not os.path.exists('instance'):
            os.makedirs('instance')
            print("✅ Created instance directory")
        
        if not os.path.exists(db_path):
            print("📦 Initializing database with sample data...")
            init_sample_data()
        else:
            print("✅ Database already exists - preserving existing data")
    
    # Create production configuration
    create_production_config()
    
    # Create deployment files
    create_deployment_files()
    
    print("\n🎉 Deployment preparation complete!")
    print("\n📋 Next steps:")
    print("1. Upload all files to GitHub")
    print("2. Deploy to your hosting platform")
    print("3. Set environment variables if needed")
    print("4. Run the application")

def create_production_config():
    """Create production configuration"""
    
    config_content = '''# Production Configuration for ATE MEG's FROZEN FOODS POS

import os

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-production-secret-key-here'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///frozen_foods_pos.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

class DevelopmentConfig(Config):
    DEBUG = True

class ProductionConfig(Config):
    DEBUG = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
'''
    
    with open('config.py', 'w') as f:
        f.write(config_content)
    
    print("✅ Created production configuration")

def create_deployment_files():
    """Create deployment files"""
    
    # Create Procfile for Heroku
    procfile_content = "web: python app.py\n"
    with open('Procfile', 'w') as f:
        f.write(procfile_content)
    
    # Create runtime.txt for Python version
    runtime_content = "python-3.10.11\n"
    with open('runtime.txt', 'w') as f:
        f.write(runtime_content)
    
    # Update requirements.txt with production dependencies
    requirements_content = """Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Werkzeug==2.3.7
gunicorn==21.2.0
"""
    with open('requirements.txt', 'w') as f:
        f.write(requirements_content)
    
    print("✅ Created deployment files (Procfile, runtime.txt, updated requirements.txt)")

def create_git_commands():
    """Create git commands file"""
    
    git_commands = '''# Git Commands for Deployment

# Initialize repository and add files
git init
git add .
git commit -m "Initial commit - ATE MEG's FROZEN FOODS POS System"

# Set up GitHub repository
git branch -M main
git remote add origin https://github.com/laganzonj/FROZEN_FOODS.git

# Push to GitHub
git push -u origin main

# For subsequent updates:
# git add .
# git commit -m "Update description"
# git push origin main
'''
    
    with open('git_commands.txt', 'w') as f:
        f.write(git_commands)
    
    print("✅ Created git commands reference file")

if __name__ == "__main__":
    prepare_for_deployment()
    create_git_commands()
